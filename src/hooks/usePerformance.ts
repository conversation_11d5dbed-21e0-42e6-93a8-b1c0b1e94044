import { useEffect, useRef } from 'react'
import { trackPerformance, trackError } from '@/lib/analytics'

interface PerformanceMetrics {
  renderTime: number
  componentMountTime: number
  rerenderCount: number
}

/**
 * Hook for monitoring component performance
 */
export function usePerformance(componentName: string) {
  const mountTime = useRef<number>(Date.now())
  const renderCount = useRef<number>(0)
  const lastRenderTime = useRef<number>(Date.now())

  useEffect(() => {
    const componentMountTime = Date.now() - mountTime.current
    renderCount.current += 1

    // Track component mount time if it's the first render
    if (renderCount.current === 1) {
      console.log(`${componentName} mounted in ${componentMountTime}ms`)
      
      // Track slow component mounts (>100ms)
      if (componentMountTime > 100) {
        trackError(new Error(`Slow component mount: ${componentName}`), {
          componentName,
          mountTime: componentMountTime,
          type: 'performance_warning',
        })
      }
    }

    lastRenderTime.current = Date.now()
  })

  // Track component unmount
  useEffect(() => {
    return () => {
      const totalLifetime = Date.now() - mountTime.current
      console.log(`${componentName} unmounted after ${totalLifetime}ms, ${renderCount.current} renders`)
    }
  }, [componentName])

  return {
    renderCount: renderCount.current,
    mountTime: mountTime.current,
  }
}

/**
 * Hook for tracking page performance
 */
export function usePagePerformance() {
  useEffect(() => {
    // Track performance metrics when component mounts
    const timer = setTimeout(() => {
      trackPerformance()
    }, 1000) // Wait 1 second for page to stabilize

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Track unhandled errors
    const handleError = (event: ErrorEvent) => {
      trackError(new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        type: 'unhandled_error',
      })
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      trackError(new Error('Unhandled Promise Rejection'), {
        reason: event.reason,
        type: 'unhandled_rejection',
      })
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])
}

/**
 * Hook for measuring render performance
 */
export function useRenderPerformance(componentName: string, dependencies: any[] = []) {
  const renderStartTime = useRef<number>(0)
  const renderCount = useRef<number>(0)

  useEffect(() => {
    renderStartTime.current = performance.now()
    renderCount.current += 1
  })

  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current
    
    // Log slow renders (>16ms for 60fps)
    if (renderTime > 16) {
      console.warn(`Slow render in ${componentName}: ${renderTime.toFixed(2)}ms`)
      
      // Track very slow renders (>50ms)
      if (renderTime > 50) {
        trackError(new Error(`Very slow render: ${componentName}`), {
          componentName,
          renderTime,
          renderCount: renderCount.current,
          dependencies: dependencies.length,
          type: 'performance_warning',
        })
      }
    }
  }, dependencies)

  return renderCount.current
}

/**
 * Hook for tracking user interactions
 */
export function useInteractionTracking() {
  useEffect(() => {
    let interactionCount = 0
    let lastInteractionTime = Date.now()

    const trackInteraction = (type: string) => {
      interactionCount += 1
      const timeSinceLastInteraction = Date.now() - lastInteractionTime
      lastInteractionTime = Date.now()

      // Track user engagement patterns
      if (timeSinceLastInteraction > 30000) { // 30 seconds of inactivity
        console.log('User returned after inactivity')
      }
    }

    const handleClick = () => trackInteraction('click')
    const handleKeydown = () => trackInteraction('keydown')
    const handleScroll = () => trackInteraction('scroll')

    document.addEventListener('click', handleClick)
    document.addEventListener('keydown', handleKeydown)
    document.addEventListener('scroll', handleScroll)

    return () => {
      document.removeEventListener('click', handleClick)
      document.removeEventListener('keydown', handleKeydown)
      document.removeEventListener('scroll', handleScroll)
      
      console.log(`Session ended with ${interactionCount} interactions`)
    }
  }, [])
}
