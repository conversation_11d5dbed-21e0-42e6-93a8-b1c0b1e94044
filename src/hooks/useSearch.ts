import { useState, useMemo } from 'react'
import { useDebounce } from './useDebounce'
import { useCashbackData } from './useCashbackData'
import type { SearchFilters, CashbackComparison } from '@/types'
import { SEARCH_CONFIG } from '@/constants'

/**
 * Custom hook for search functionality
 */
export function useSearch() {
  const { filterComparisons } = useCashbackData()
  const [query, setQuery] = useState('')
  const [filters, setFilters] = useState<SearchFilters>({
    sortBy: 'rate',
    sortOrder: 'desc',
  })

  // Debounce search query
  const debouncedQuery = useDebounce(query, SEARCH_CONFIG.debounceDelay)

  // Create search filters with debounced query
  const searchFilters = useMemo((): SearchFilters => ({
    ...filters,
    query: debouncedQuery.length >= SEARCH_CONFIG.minQueryLength ? debouncedQuery : undefined,
  }), [filters, debouncedQuery])

  // Get filtered results
  const results = useMemo((): CashbackComparison[] => {
    return filterComparisons(searchFilters)
  }, [filterComparisons, searchFilters])

  // Update search query
  const updateQuery = (newQuery: string) => {
    setQuery(newQuery)
  }

  // Update filters
  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  // Clear search
  const clearSearch = () => {
    setQuery('')
    setFilters({
      sortBy: 'rate',
      sortOrder: 'desc',
    })
  }

  // Check if search is active
  const isSearchActive = query.length > 0 || 
    filters.categories?.length || 
    filters.platforms?.length ||
    filters.minRate !== undefined ||
    filters.maxRate !== undefined ||
    filters.featured !== undefined ||
    filters.verified !== undefined

  return {
    query,
    filters,
    searchFilters,
    results,
    updateQuery,
    updateFilters,
    clearSearch,
    isSearchActive,
    isLoading: query !== debouncedQuery, // Loading while debouncing
  }
}
