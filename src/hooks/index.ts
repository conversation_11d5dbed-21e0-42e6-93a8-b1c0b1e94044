// Custom hooks exports
export { useDebounce } from './useDebounce'
export { useLocalStorage } from './useLocalStorage'
export { useMediaQuery, useIsMobile, useIsTablet, useIsDesktop } from './useMediaQuery'
export { useIntersectionObserver } from './useIntersectionObserver'
export { useCashbackData } from './useCashbackData'
export { useSearch } from './useSearch'
export { useTheme } from './useTheme'
export { usePerformance, usePagePerformance, useRenderPerformance, useInteractionTracking } from './usePerformance'
export { useFocusManagement, useFocusAnnouncement, useKeyboardNavigation } from './useFocusManagement'
