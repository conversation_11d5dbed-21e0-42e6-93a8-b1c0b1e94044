import { useEffect, useRef } from 'react'

/**
 * Hook for managing focus in modals and overlays
 */
export function useFocusManagement(isOpen: boolean) {
  const containerRef = useRef<HTMLElement>(null)
  const previousActiveElement = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!isOpen) return

    // Store the currently focused element
    previousActiveElement.current = document.activeElement as HTMLElement

    // Focus the container or first focusable element
    const container = containerRef.current
    if (container) {
      const firstFocusable = container.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement

      if (firstFocusable) {
        firstFocusable.focus()
      } else {
        container.focus()
      }
    }

    // Trap focus within the container
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab' || !container) return

      const focusableElements = container.querySelectorAll(
        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
      ) as NodeListOf<HTMLElement>

      const firstElement = focusableElements[0]
      const lastElement = focusableElements[focusableElements.length - 1]

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement?.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement?.focus()
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      
      // Restore focus to the previously focused element
      if (previousActiveElement.current) {
        previousActiveElement.current.focus()
      }
    }
  }, [isOpen])

  return containerRef
}

/**
 * Hook for managing focus announcements for screen readers
 */
export function useFocusAnnouncement() {
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', priority)
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message

    document.body.appendChild(announcement)

    // Remove the announcement after it's been read
    setTimeout(() => {
      document.body.removeChild(announcement)
    }, 1000)
  }

  return { announce }
}

/**
 * Hook for keyboard navigation
 */
export function useKeyboardNavigation(
  items: HTMLElement[],
  options: {
    loop?: boolean
    orientation?: 'horizontal' | 'vertical'
    onSelect?: (index: number) => void
  } = {}
) {
  const { loop = true, orientation = 'vertical', onSelect } = options
  const currentIndex = useRef(0)

  const handleKeyDown = (event: KeyboardEvent) => {
    const { key } = event
    let newIndex = currentIndex.current

    const isVertical = orientation === 'vertical'
    const nextKey = isVertical ? 'ArrowDown' : 'ArrowRight'
    const prevKey = isVertical ? 'ArrowUp' : 'ArrowLeft'

    switch (key) {
      case nextKey:
        event.preventDefault()
        newIndex = currentIndex.current + 1
        if (newIndex >= items.length) {
          newIndex = loop ? 0 : items.length - 1
        }
        break

      case prevKey:
        event.preventDefault()
        newIndex = currentIndex.current - 1
        if (newIndex < 0) {
          newIndex = loop ? items.length - 1 : 0
        }
        break

      case 'Home':
        event.preventDefault()
        newIndex = 0
        break

      case 'End':
        event.preventDefault()
        newIndex = items.length - 1
        break

      case 'Enter':
      case ' ':
        event.preventDefault()
        onSelect?.(currentIndex.current)
        return

      default:
        return
    }

    currentIndex.current = newIndex
    items[newIndex]?.focus()
  }

  const setCurrentIndex = (index: number) => {
    currentIndex.current = index
  }

  return {
    handleKeyDown,
    setCurrentIndex,
    currentIndex: currentIndex.current,
  }
}
