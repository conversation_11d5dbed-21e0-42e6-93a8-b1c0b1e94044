import { useState, useEffect } from 'react'
import { useLocalStorage } from './useLocalStorage'
import { STORAGE_KEYS } from '@/constants'

export type Theme = 'light' | 'dark' | 'system'

interface ThemeContextType {
  theme: Theme
  resolvedTheme: 'light' | 'dark'
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
}

/**
 * Custom hook for theme management with system preference detection
 */
export function useTheme(): ThemeContextType {
  const [storedTheme, setStoredTheme] = useLocalStorage<Theme>(STORAGE_KEYS.theme, 'system')
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light')

  // Detect system theme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light')
    }

    // Set initial value
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light')

    // Listen for changes
    mediaQuery.addEventListener('change', handleChange)

    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement
    const resolvedTheme = storedTheme === 'system' ? systemTheme : storedTheme

    if (resolvedTheme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }

    // Update meta theme-color
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content', 
        resolvedTheme === 'dark' ? '#1F2937' : '#10B981'
      )
    }
  }, [storedTheme, systemTheme])

  const resolvedTheme = storedTheme === 'system' ? systemTheme : storedTheme

  const setTheme = (theme: Theme) => {
    setStoredTheme(theme)
  }

  const toggleTheme = () => {
    if (storedTheme === 'light') {
      setTheme('dark')
    } else if (storedTheme === 'dark') {
      setTheme('system')
    } else {
      setTheme('light')
    }
  }

  return {
    theme: storedTheme,
    resolvedTheme,
    setTheme,
    toggleTheme,
  }
}
