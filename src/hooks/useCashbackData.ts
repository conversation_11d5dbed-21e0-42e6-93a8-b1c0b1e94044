import { useState, useEffect, useMemo } from 'react'
import type { CashbackComparison, Store, CashbackRate, SearchFilters } from '@/types'
import { STORES } from '@/data/stores'
import { PLATFORMS } from '@/data/platforms'

// Mock cashback rates data
const MOCK_RATES: CashbackRate[] = [
  {
    id: '1',
    storeId: 'nike',
    platform: PLATFORMS[0], // Meliuz
    rate: 8.5,
    rateType: 'percentage',
    conditions: ['Válido para produtos com preço cheio'],
    validUntil: '2024-12-31',
    isPromotional: true,
    lastVerified: '2024-12-21T10:00:00Z',
    trend: 'up',
    trendPercentage: 15,
  },
  {
    id: '2',
    storeId: 'nike',
    platform: PLATFORMS[1], // Rakuten
    rate: 7.0,
    rateType: 'percentage',
    isPromotional: false,
    lastVerified: '2024-12-21T09:30:00Z',
    trend: 'stable',
  },
  {
    id: '3',
    storeId: 'amazon',
    platform: PLATFORMS[0], // Meliuz
    rate: 5.5,
    rateType: 'percentage',
    isPromotional: false,
    lastVerified: '2024-12-21T09:00:00Z',
    trend: 'up',
    trendPercentage: 8,
  },
  {
    id: '4',
    storeId: 'amazon',
    platform: PLATFORMS[1], // Rakuten
    rate: 4.2,
    rateType: 'percentage',
    isPromotional: false,
    lastVerified: '2024-12-21T08:45:00Z',
    trend: 'down',
    trendPercentage: 5,
  },
  {
    id: '5',
    storeId: 'magazine-luiza',
    platform: PLATFORMS[0], // Meliuz
    rate: 6.8,
    rateType: 'percentage',
    isPromotional: false,
    lastVerified: '2024-12-21T08:30:00Z',
    trend: 'stable',
  },
]

/**
 * Custom hook for managing cashback data
 */
export function useCashbackData() {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Simulate API loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Create cashback comparisons from stores and rates
  const comparisons = useMemo((): CashbackComparison[] => {
    return STORES.map(store => {
      const storeRates = MOCK_RATES.filter(rate => rate.storeId === store.id)
      const bestRate = storeRates.reduce((best, current) => 
        current.rate > best.rate ? current : best, 
        storeRates[0] || { rate: 0 } as CashbackRate
      )
      const averageRate = storeRates.length > 0 
        ? storeRates.reduce((sum, rate) => sum + rate.rate, 0) / storeRates.length 
        : 0

      return {
        storeId: store.id,
        store,
        rates: storeRates,
        bestRate,
        averageRate,
        totalPlatforms: storeRates.length,
        lastUpdated: store.lastUpdated,
      }
    })
  }, [])

  // Filter and search functionality
  const filterComparisons = (filters: SearchFilters): CashbackComparison[] => {
    let filtered = [...comparisons]

    // Filter by search query
    if (filters.query) {
      const query = filters.query.toLowerCase()
      filtered = filtered.filter(comp => 
        comp.store.name.toLowerCase().includes(query) ||
        comp.store.description.toLowerCase().includes(query) ||
        comp.store.category.name.toLowerCase().includes(query)
      )
    }

    // Filter by categories
    if (filters.categories && filters.categories.length > 0) {
      filtered = filtered.filter(comp => 
        filters.categories!.includes(comp.store.category.id)
      )
    }

    // Filter by platforms
    if (filters.platforms && filters.platforms.length > 0) {
      filtered = filtered.filter(comp => 
        comp.rates.some(rate => 
          filters.platforms!.includes(rate.platform.id)
        )
      )
    }

    // Filter by minimum rate
    if (filters.minRate !== undefined) {
      filtered = filtered.filter(comp => comp.bestRate.rate >= filters.minRate!)
    }

    // Filter by maximum rate
    if (filters.maxRate !== undefined) {
      filtered = filtered.filter(comp => comp.bestRate.rate <= filters.maxRate!)
    }

    // Filter by featured
    if (filters.featured !== undefined) {
      filtered = filtered.filter(comp => comp.store.featured === filters.featured)
    }

    // Filter by verified
    if (filters.verified !== undefined) {
      filtered = filtered.filter(comp => comp.store.verified === filters.verified)
    }

    // Sort results
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        let comparison = 0
        
        switch (filters.sortBy) {
          case 'rate':
            comparison = b.bestRate.rate - a.bestRate.rate
            break
          case 'name':
            comparison = a.store.name.localeCompare(b.store.name)
            break
          case 'popularity':
            comparison = b.store.trustScore - a.store.trustScore
            break
          case 'lastUpdated':
            comparison = new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
            break
        }

        return filters.sortOrder === 'asc' ? comparison : -comparison
      })
    }

    return filtered
  }

  // Get featured comparisons
  const featuredComparisons = useMemo(() => 
    comparisons.filter(comp => comp.store.featured), 
    [comparisons]
  )

  // Get top rates
  const topRates = useMemo(() => 
    [...comparisons]
      .sort((a, b) => b.bestRate.rate - a.bestRate.rate)
      .slice(0, 6), 
    [comparisons]
  )

  return {
    comparisons,
    featuredComparisons,
    topRates,
    filterComparisons,
    loading,
    error,
    stores: STORES,
    platforms: PLATFORMS,
  }
}
