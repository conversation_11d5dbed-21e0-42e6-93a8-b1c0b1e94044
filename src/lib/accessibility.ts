/**
 * Accessibility utilities for color contrast and WCAG compliance
 */

/**
 * Calculate relative luminance of a color
 * Based on WCAG 2.1 guidelines
 */
function getLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
  })
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
}

/**
 * Calculate contrast ratio between two colors
 */
export function getContrastRatio(color1: string, color2: string): number {
  const rgb1 = hexToRgb(color1)
  const rgb2 = hexToRgb(color2)
  
  if (!rgb1 || !rgb2) return 1
  
  const lum1 = getLuminance(rgb1.r, rgb1.g, rgb1.b)
  const lum2 = getLuminance(rgb2.r, rgb2.g, rgb2.b)
  
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)
  
  return (brightest + 0.05) / (darkest + 0.05)
}

/**
 * Convert hex color to RGB
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

/**
 * Check if color combination meets WCAG contrast requirements
 */
export function meetsContrastRequirement(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean {
  const ratio = getContrastRatio(foreground, background)
  
  if (level === 'AAA') {
    return size === 'large' ? ratio >= 4.5 : ratio >= 7
  }
  
  return size === 'large' ? ratio >= 3 : ratio >= 4.5
}

/**
 * Get accessible color for text based on background
 */
export function getAccessibleTextColor(backgroundColor: string): string {
  const whiteContrast = getContrastRatio('#FFFFFF', backgroundColor)
  const blackContrast = getContrastRatio('#000000', backgroundColor)
  
  return whiteContrast > blackContrast ? '#FFFFFF' : '#000000'
}

/**
 * Generate accessible color palette
 */
export const accessibleColors = {
  // High contrast colors for text
  text: {
    primary: '#1F2937', // Gray-800
    secondary: '#4B5563', // Gray-600
    light: '#6B7280', // Gray-500
    inverse: '#F9FAFB', // Gray-50
  },
  
  // Background colors with good contrast
  background: {
    primary: '#FFFFFF',
    secondary: '#F9FAFB', // Gray-50
    tertiary: '#F3F4F6', // Gray-100
    dark: '#1F2937', // Gray-800
  },
  
  // Status colors with sufficient contrast
  status: {
    success: {
      background: '#ECFDF5', // Green-50
      text: '#047857', // Green-700
      border: '#10B981', // Green-500
    },
    warning: {
      background: '#FFFBEB', // Amber-50
      text: '#92400E', // Amber-700
      border: '#F59E0B', // Amber-500
    },
    error: {
      background: '#FEF2F2', // Red-50
      text: '#B91C1C', // Red-700
      border: '#EF4444', // Red-500
    },
    info: {
      background: '#EFF6FF', // Blue-50
      text: '#1D4ED8', // Blue-700
      border: '#3B82F6', // Blue-500
    },
  },
}

/**
 * Focus ring utilities for keyboard navigation
 */
export const focusRing = {
  default: 'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2',
  inset: 'focus:outline-none focus:ring-2 focus:ring-inset focus:ring-emerald-500',
  large: 'focus:outline-none focus:ring-4 focus:ring-emerald-500 focus:ring-offset-2',
  dark: 'focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:ring-offset-2 focus:ring-offset-gray-800',
}

/**
 * Screen reader utilities
 */
export const screenReader = {
  only: 'sr-only',
  focusable: 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-emerald-600 focus:text-white focus:rounded-lg',
}

/**
 * Motion preferences utilities
 */
export function respectsReducedMotion(): boolean {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

/**
 * High contrast mode detection
 */
export function prefersHighContrast(): boolean {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-contrast: high)').matches
}

/**
 * Color scheme preference detection
 */
export function getColorSchemePreference(): 'light' | 'dark' | null {
  if (typeof window === 'undefined') return null
  
  if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return 'dark'
  }
  
  if (window.matchMedia('(prefers-color-scheme: light)').matches) {
    return 'light'
  }
  
  return null
}

/**
 * Validate ARIA attributes
 */
export function validateAriaAttributes(element: HTMLElement): string[] {
  const issues: string[] = []
  
  // Check for required ARIA labels
  if (element.getAttribute('role') === 'button' && !element.getAttribute('aria-label') && !element.textContent?.trim()) {
    issues.push('Button elements should have accessible text or aria-label')
  }
  
  // Check for proper ARIA relationships
  const describedBy = element.getAttribute('aria-describedby')
  if (describedBy) {
    const describedElement = document.getElementById(describedBy)
    if (!describedElement) {
      issues.push(`aria-describedby references non-existent element: ${describedBy}`)
    }
  }
  
  const labelledBy = element.getAttribute('aria-labelledby')
  if (labelledBy) {
    const labelElement = document.getElementById(labelledBy)
    if (!labelElement) {
      issues.push(`aria-labelledby references non-existent element: ${labelledBy}`)
    }
  }
  
  return issues
}
