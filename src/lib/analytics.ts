import type { AnalyticsEvent, PageView } from '@/types'

declare global {
  interface Window {
    gtag?: (...args: any[]) => void
    dataLayer?: any[]
  }
}

/**
 * Analytics utility for tracking user interactions and performance
 */
class Analytics {
  private isEnabled: boolean
  private sessionId: string

  constructor() {
    this.isEnabled = typeof window !== 'undefined' && process.env.NODE_ENV === 'production'
    this.sessionId = this.generateSessionId()
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Initialize Google Analytics
   */
  init(measurementId: string) {
    if (!this.isEnabled || !measurementId) return

    // Load Google Analytics script
    const script = document.createElement('script')
    script.async = true
    script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`
    document.head.appendChild(script)

    // Initialize gtag
    window.dataLayer = window.dataLayer || []
    window.gtag = function gtag() {
      window.dataLayer?.push(arguments)
    }

    window.gtag('js', new Date())
    window.gtag('config', measurementId, {
      page_title: document.title,
      page_location: window.location.href,
    })
  }

  /**
   * Track page views
   */
  trackPageView(pageView: Partial<PageView>) {
    if (!this.isEnabled) return

    const data: PageView = {
      page: window.location.pathname,
      title: document.title,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId,
      ...pageView,
    }

    // Send to Google Analytics
    if (window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        page_title: data.title,
        page_location: window.location.href,
      })
    }

    // Send to custom analytics endpoint
    this.sendToEndpoint('/api/analytics/pageview', data)
  }

  /**
   * Track custom events
   */
  trackEvent(event: AnalyticsEvent) {
    if (!this.isEnabled) return

    // Send to Google Analytics
    if (window.gtag) {
      window.gtag('event', event.action, {
        event_category: event.category,
        event_label: event.label,
        value: event.value,
        custom_parameters: event.properties,
      })
    }

    // Send to custom analytics endpoint
    this.sendToEndpoint('/api/analytics/event', {
      ...event,
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId,
    })
  }

  /**
   * Track search queries
   */
  trackSearch(query: string, results: number, filters?: any) {
    this.trackEvent({
      event: 'search',
      category: 'engagement',
      action: 'search_query',
      label: query,
      value: results,
      properties: {
        query,
        results_count: results,
        filters,
      },
    })
  }

  /**
   * Track cashback card interactions
   */
  trackCashbackCardClick(storeId: string, storeName: string, rate: number) {
    this.trackEvent({
      event: 'cashback_card_click',
      category: 'engagement',
      action: 'card_click',
      label: storeName,
      value: rate,
      properties: {
        store_id: storeId,
        store_name: storeName,
        cashback_rate: rate,
      },
    })
  }

  /**
   * Track performance metrics
   */
  trackPerformance() {
    if (!this.isEnabled || !window.performance) return

    // Core Web Vitals
    this.trackWebVitals()

    // Custom performance metrics
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      this.trackEvent({
        event: 'performance',
        category: 'technical',
        action: 'page_load',
        value: Math.round(navigation.loadEventEnd - navigation.loadEventStart),
        properties: {
          dns_time: Math.round(navigation.domainLookupEnd - navigation.domainLookupStart),
          connect_time: Math.round(navigation.connectEnd - navigation.connectStart),
          response_time: Math.round(navigation.responseEnd - navigation.responseStart),
          dom_load_time: Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart),
          total_load_time: Math.round(navigation.loadEventEnd - navigation.loadEventStart),
        },
      })
    }
  }

  /**
   * Track Core Web Vitals
   */
  private trackWebVitals() {
    // LCP (Largest Contentful Paint)
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      
      this.trackEvent({
        event: 'web_vital',
        category: 'performance',
        action: 'LCP',
        value: Math.round(lastEntry.startTime),
        properties: {
          metric: 'LCP',
          value: lastEntry.startTime,
          rating: lastEntry.startTime > 4000 ? 'poor' : lastEntry.startTime > 2500 ? 'needs_improvement' : 'good',
        },
      })
    }).observe({ entryTypes: ['largest-contentful-paint'] })

    // FID (First Input Delay)
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        this.trackEvent({
          event: 'web_vital',
          category: 'performance',
          action: 'FID',
          value: Math.round(entry.processingStart - entry.startTime),
          properties: {
            metric: 'FID',
            value: entry.processingStart - entry.startTime,
            rating: entry.processingStart - entry.startTime > 300 ? 'poor' : entry.processingStart - entry.startTime > 100 ? 'needs_improvement' : 'good',
          },
        })
      })
    }).observe({ entryTypes: ['first-input'] })

    // CLS (Cumulative Layout Shift)
    let clsValue = 0
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      })

      this.trackEvent({
        event: 'web_vital',
        category: 'performance',
        action: 'CLS',
        value: Math.round(clsValue * 1000),
        properties: {
          metric: 'CLS',
          value: clsValue,
          rating: clsValue > 0.25 ? 'poor' : clsValue > 0.1 ? 'needs_improvement' : 'good',
        },
      })
    }).observe({ entryTypes: ['layout-shift'] })
  }

  /**
   * Track errors
   */
  trackError(error: Error, errorInfo?: any) {
    this.trackEvent({
      event: 'error',
      category: 'technical',
      action: 'javascript_error',
      label: error.message,
      properties: {
        error_message: error.message,
        error_stack: error.stack,
        error_info: errorInfo,
        user_agent: navigator.userAgent,
        url: window.location.href,
      },
    })
  }

  /**
   * Send data to custom analytics endpoint
   */
  private async sendToEndpoint(endpoint: string, data: any) {
    try {
      await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
    } catch (error) {
      console.warn('Failed to send analytics data:', error)
    }
  }
}

// Export singleton instance
export const analytics = new Analytics()

// Convenience functions
export const trackPageView = (data?: Partial<PageView>) => analytics.trackPageView(data || {})
export const trackEvent = (event: AnalyticsEvent) => analytics.trackEvent(event)
export const trackSearch = (query: string, results: number, filters?: any) => 
  analytics.trackSearch(query, results, filters)
export const trackCashbackCardClick = (storeId: string, storeName: string, rate: number) =>
  analytics.trackCashbackCardClick(storeId, storeName, rate)
export const trackPerformance = () => analytics.trackPerformance()
export const trackError = (error: Error, errorInfo?: any) => analytics.trackError(error, errorInfo)
