import { type ClassValue, clsx } from 'clsx'

/**
 * Utility function to merge class names with clsx
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs)
}

/**
 * Format currency values for Brazilian Real
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value)
}

/**
 * Format percentage values
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`
}

/**
 * Format relative time in Portuguese
 */
export function formatRelativeTime(date: string | Date): string {
  const now = new Date()
  const targetDate = typeof date === 'string' ? new Date(date) : date
  const diffInHours = Math.floor((now.getTime() - targetDate.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'há poucos minutos'
  } else if (diffInHours < 24) {
    return `há ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `há ${diffInDays} dia${diffInDays > 1 ? 's' : ''}`
  }
}

/**
 * Debounce function for search inputs
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Generate unique ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

/**
 * Calculate best cashback rate from platforms
 */
export function getBestRate(platforms: Array<{ rate: number }>): number {
  return Math.max(...platforms.map(p => p.rate))
}

/**
 * Calculate average cashback rate
 */
export function getAverageRate(platforms: Array<{ rate: number }>): number {
  const sum = platforms.reduce((acc, p) => acc + p.rate, 0)
  return sum / platforms.length
}

/**
 * Sort platforms by rate (descending)
 */
export function sortByRate<T extends { rate: number }>(platforms: T[]): T[] {
  return [...platforms].sort((a, b) => b.rate - a.rate)
}

/**
 * Get trend icon based on trend type
 */
export function getTrendColor(trend: 'up' | 'down' | 'stable'): string {
  switch (trend) {
    case 'up':
      return 'text-green-500'
    case 'down':
      return 'text-red-500'
    case 'stable':
      return 'text-gray-500'
    default:
      return 'text-gray-500'
  }
}

/**
 * Sanitize search query
 */
export function sanitizeSearchQuery(query: string): string {
  return query.trim().toLowerCase().replace(/[^\w\s]/gi, '')
}

/**
 * Check if device is mobile
 */
export function isMobile(): boolean {
  if (typeof window === 'undefined') return false
  return window.innerWidth < 768
}

/**
 * Scroll to element smoothly
 */
export function scrollToElement(elementId: string): void {
  const element = document.getElementById(elementId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

/**
 * Copy text to clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return false
  }
}

/**
 * Format number with thousands separator
 */
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('pt-BR').format(num)
}

/**
 * Get platform color based on name
 */
export function getPlatformColor(platformName: string): string {
  const colors: Record<string, string> = {
    'meliuz': '#8B5CF6',
    'rakuten': '#3B82F6',
    'topcashback': '#10B981',
    'honey': '#F59E0B',
    'inter': '#FF6B35',
    'pan': '#0066CC',
  }
  
  const key = platformName.toLowerCase().replace(/\s+/g, '')
  return colors[key] || '#6B7280'
}
