'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline'
import { Button } from '@/ui'
import { useSearch, useDebounce } from '@/hooks'
import { cn } from '@/lib/utils'
import type { SearchFilters } from '@/types'

interface SearchBarProps {
  onSearch?: (query: string) => void
  onFiltersChange?: (filters: SearchFilters) => void
  placeholder?: string
  showFilters?: boolean
  className?: string
}

const categories = [
  'Moda & Vestuário',
  'Eletrônicos',
  'Casa & Decoração',
  'Marketplace',
  'Saúde & Beleza',
  'Esportes',
  'Livros & Educação',
  'Viagem & Turismo',
]

const platforms = [
  'Meliuz',
  'Rakuten',
  'TopCashback',
  'Honey',
  'Inter Shopping',
  'Banco Pan',
]

export function SearchBar({
  onSearch,
  onFiltersChange,
  placeholder = 'Buscar lojas, categorias ou marcas...',
  showFilters = true,
  className,
}: SearchBarProps) {
  const { query, filters, updateQuery, updateFilters, clearSearch, isSearchActive } = useSearch()
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const debouncedQuery = useDebounce(query, 300)

  // Call external handlers
  useEffect(() => {
    if (onSearch) {
      onSearch(debouncedQuery)
    }
  }, [debouncedQuery, onSearch])

  useEffect(() => {
    if (onFiltersChange) {
      onFiltersChange(filters)
    }
  }, [filters, onFiltersChange])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateQuery(e.target.value)
  }

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    updateFilters({ [key]: value })
  }

  const handleClearAll = () => {
    clearSearch()
    setShowAdvancedFilters(false)
    inputRef.current?.focus()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsFocused(false)
      inputRef.current?.blur()
    }
  }

  return (
    <div className={cn('relative w-full max-w-2xl mx-auto', className)}>
      {/* Main Search Input */}
      <div
        className={cn(
          'relative flex items-center bg-white rounded-2xl shadow-lg transition-all duration-300',
          isFocused ? 'ring-2 ring-emerald-500 shadow-xl' : 'shadow-lg',
          'border border-gray-200 hover:border-emerald-300'
        )}
      >
        <div className="absolute left-4 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
        </div>

        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="flex-1 pl-12 pr-4 py-4 bg-transparent text-gray-900 placeholder-gray-500 focus:outline-none text-lg"
        />

        <div className="flex items-center space-x-2 pr-4">
          {isSearchActive && (
            <motion.button
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              onClick={handleClearAll}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Limpar busca"
            >
              <XMarkIcon className="w-5 h-5" />
            </motion.button>
          )}

          {showFilters && (
            <Button
              variant={showAdvancedFilters ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Filtros</span>
            </Button>
          )}
        </div>
      </div>

      {/* Advanced Filters Panel */}
      <AnimatePresence>
        {showAdvancedFilters && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white rounded-2xl shadow-xl border border-gray-200 p-6 z-50"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-poppins font-bold text-gray-900 flex items-center">
                <AdjustmentsHorizontalIcon className="w-5 h-5 mr-2 text-emerald-600" />
                Filtros Avançados
              </h3>
              <button
                onClick={() => setShowAdvancedFilters(false)}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Categories Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Categorias
                </label>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {categories.map((category) => (
                    <label key={category} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.categories?.includes(category) || false}
                        onChange={(e) => {
                          const currentCategories = filters.categories || []
                          const newCategories = e.target.checked
                            ? [...currentCategories, category]
                            : currentCategories.filter(c => c !== category)
                          handleFilterChange('categories', newCategories.length > 0 ? newCategories : undefined)
                        }}
                        className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{category}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Platforms Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Plataformas
                </label>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {platforms.map((platform) => (
                    <label key={platform} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.platforms?.includes(platform) || false}
                        onChange={(e) => {
                          const currentPlatforms = filters.platforms || []
                          const newPlatforms = e.target.checked
                            ? [...currentPlatforms, platform]
                            : currentPlatforms.filter(p => p !== platform)
                          handleFilterChange('platforms', newPlatforms.length > 0 ? newPlatforms : undefined)
                        }}
                        className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{platform}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Rate Range Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Taxa de Cashback
                </label>
                <div className="space-y-4">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">
                      Taxa mínima: {filters.minRate || 0}%
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="20"
                      step="0.5"
                      value={filters.minRate || 0}
                      onChange={(e) => handleFilterChange('minRate', parseFloat(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">
                      Taxa máxima: {filters.maxRate || 20}%
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="20"
                      step="0.5"
                      value={filters.maxRate || 20}
                      onChange={(e) => handleFilterChange('maxRate', parseFloat(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Options */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex flex-wrap gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.featured || false}
                    onChange={(e) => handleFilterChange('featured', e.target.checked || undefined)}
                    className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Apenas em destaque</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.verified || false}
                    onChange={(e) => handleFilterChange('verified', e.target.checked || undefined)}
                    className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Apenas verificadas</span>
                </label>
              </div>
            </div>

            {/* Filter Actions */}
            <div className="mt-6 flex justify-between">
              <Button
                variant="outline"
                onClick={handleClearAll}
                className="text-gray-600"
              >
                Limpar Filtros
              </Button>
              
              <Button
                onClick={() => setShowAdvancedFilters(false)}
              >
                Aplicar Filtros
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
