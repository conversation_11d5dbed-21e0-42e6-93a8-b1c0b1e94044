import React, { memo } from 'react'
import { motion } from 'framer-motion'
import {
  ShieldCheckIcon,
  ArrowTopRightOnSquareIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  MinusIcon,
  StarIcon,
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import { Card, CardContent, CardFooter, CardHeader } from '@/ui'
import { formatPercentage, formatRelativeTime, getTrendColor, cn } from '@/lib/utils'
import type { CashbackComparison } from '@/types'

interface CashbackRateCardProps {
  comparison: CashbackComparison
  featured?: boolean
  compact?: boolean
  showPlatforms?: boolean
  className?: string
}

const TrendIcon = memo(({ trend, trendPercent }: { trend: string; trendPercent?: number }) => {
  const iconClass = cn('w-4 h-4', getTrendColor(trend))
  
  switch (trend) {
    case 'up':
      return <ArrowUpIcon className={iconClass} />
    case 'down':
      return <ArrowDownIcon className={iconClass} />
    case 'stable':
    default:
      return <MinusIcon className={iconClass} />
  }
})

TrendIcon.displayName = 'TrendIcon'

const TrustScore = memo(({ score }: { score: number }) => {
  const fullStars = Math.floor(score)
  const hasHalfStar = score % 1 >= 0.5

  return (
    <div className="flex items-center space-x-1">
      {Array.from({ length: 5 }, (_, i) => {
        if (i < fullStars) {
          return <StarIconSolid key={i} className="w-3 h-3 text-yellow-400" />
        } else if (i === fullStars && hasHalfStar) {
          return <StarIcon key={i} className="w-3 h-3 text-yellow-400" />
        } else {
          return <StarIcon key={i} className="w-3 h-3 text-gray-300" />
        }
      })}
      <span className="text-xs text-gray-600 ml-1">{score.toFixed(1)}</span>
    </div>
  )
})

TrustScore.displayName = 'TrustScore'

export const CashbackRateCard = memo(({
  comparison,
  featured = false,
  compact = false,
  showPlatforms = true,
  className,
}: CashbackRateCardProps) => {
  const { store, bestRate, rates, averageRate, totalPlatforms, lastUpdated } = comparison

  return (
    <Card
      variant={featured ? 'featured' : 'default'}
      className={cn('relative overflow-hidden', className)}
    >
      {featured && (
        <div className="absolute top-0 right-0 bg-gradient-to-l from-emerald-500 to-emerald-400 text-white px-3 py-1 text-xs font-bold rounded-bl-lg">
          DESTAQUE
        </div>
      )}

      <CardHeader>
        <div className="flex items-center space-x-3">
          <div 
            className="w-12 h-12 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg"
            style={{ backgroundColor: store.brandColors.primary }}
          >
            {store.name.charAt(0)}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-poppins font-bold text-gray-900 truncate">
              {store.name}
            </h3>
            <p className="text-sm text-gray-600 truncate">
              {store.category.name}
            </p>
          </div>
          {store.verified && (
            <ShieldCheckIcon className="w-5 h-5 text-emerald-500 flex-shrink-0" />
          )}
        </div>
      </CardHeader>

      <CardContent>
        {/* Best Rate Display */}
        <div className="text-center mb-4">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <span className="text-3xl font-poppins font-black text-emerald-600">
              {formatPercentage(bestRate.rate)}
            </span>
            <TrendIcon 
              trend={bestRate.trend} 
              trendPercent={bestRate.trendPercentage} 
            />
          </div>
          <p className="text-sm text-gray-600">
            Melhor taxa de cashback
          </p>
          {bestRate.isPromotional && (
            <span className="inline-block bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full mt-1">
              Oferta Promocional
            </span>
          )}
        </div>

        {/* Platform Information */}
        {showPlatforms && !compact && (
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Plataforma líder:</span>
              <span className="font-medium text-gray-900">
                {bestRate.platform.name}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Taxa média:</span>
              <span className="font-medium text-gray-900">
                {formatPercentage(averageRate)}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Plataformas:</span>
              <span className="font-medium text-gray-900">
                {totalPlatforms} disponíveis
              </span>
            </div>
          </div>
        )}

        {/* Trust Score */}
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-gray-600">Confiabilidade:</span>
          <TrustScore score={store.trustScore} />
        </div>

        {/* Last Updated */}
        <div className="text-xs text-gray-500 text-center">
          Atualizado {formatRelativeTime(lastUpdated)}
        </div>
      </CardContent>

      <CardFooter>
        <div className="flex space-x-2">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1 bg-emerald-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <span>Ver Ofertas</span>
            <ArrowTopRightOnSquareIcon className="w-4 h-4" />
          </motion.button>
          
          {!compact && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-4 py-2 border border-emerald-200 text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors duration-200"
            >
              Comparar
            </motion.button>
          )}
        </div>
      </CardFooter>
    </Card>
  )
})

CashbackRateCard.displayName = 'CashbackRateCard'
