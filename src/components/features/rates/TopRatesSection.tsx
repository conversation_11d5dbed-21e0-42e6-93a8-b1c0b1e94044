import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { FunnelIcon, SparklesIcon } from '@heroicons/react/24/outline'
import { Section } from '@/layout'
import { Button } from '@/ui'
import { CashbackRateCard } from './CashbackRateCard'
import { useCashbackData } from '@/hooks'
import { cn } from '@/lib/utils'

const categories = [
  'Todas',
  'Moda & Vestuário',
  'Eletrônicos',
  'Casa & Decoração',
  'Marketplace',
  'Saúde & Beleza',
]

const sortOptions = [
  { value: 'rate', label: 'Maior Taxa' },
  { value: 'name', label: 'Nome A-Z' },
  { value: 'popularity', label: 'Popularidade' },
  { value: 'lastUpdated', label: 'Mais Recente' },
]

export function TopRatesSection() {
  const { topRates, loading } = useCashbackData()
  const [selectedCategory, setSelectedCategory] = useState('Todas')
  const [sortBy, setSortBy] = useState('rate')
  const [showFilters, setShowFilters] = useState(false)

  const filteredRates = useMemo(() => {
    let filtered = [...topRates]

    // Filter by category
    if (selectedCategory !== 'Todas') {
      filtered = filtered.filter(
        comparison => comparison.store.category.name === selectedCategory
      )
    }

    // Sort results
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'rate':
          return b.bestRate.rate - a.bestRate.rate
        case 'name':
          return a.store.name.localeCompare(b.store.name)
        case 'popularity':
          return b.store.trustScore - a.store.trustScore
        case 'lastUpdated':
          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
        default:
          return 0
      }
    })

    return filtered
  }, [topRates, selectedCategory, sortBy])

  if (loading) {
    return (
      <Section id="rates" variant="default">
        <div className="text-center mb-16">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-2xl h-80"></div>
            </div>
          ))}
        </div>
      </Section>
    )
  }

  return (
    <Section id="rates" variant="default">
      {/* Section Header */}
      <div className="text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="inline-flex items-center px-4 py-2 rounded-full bg-emerald-100 text-emerald-700 font-medium text-sm mb-6"
        >
          <SparklesIcon className="w-4 h-4 mr-2" />
          Taxas Atualizadas em Tempo Real
        </motion.div>

        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          viewport={{ once: true }}
          className="text-responsive-2xl font-poppins font-bold text-gray-900 mb-4"
        >
          Melhores Taxas de{' '}
          <span className="gradient-text">Cashback</span>
        </motion.h2>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-responsive-sm text-gray-600 max-w-2xl mx-auto"
        >
          Compare as taxas de cashback mais altas do mercado e maximize suas economias
          em cada compra que fizer.
        </motion.p>
      </div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        viewport={{ once: true }}
        className="mb-12"
      >
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          {/* Category Filters */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={cn(
                  'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200',
                  selectedCategory === category
                    ? 'bg-emerald-600 text-white shadow-lg'
                    : 'bg-white text-gray-600 border border-gray-200 hover:border-emerald-200 hover:text-emerald-600'
                )}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Sort and Filter Controls */}
          <div className="flex items-center space-x-4">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="w-4 h-4" />
              <span>Filtros</span>
            </Button>
          </div>
        </div>

        {/* Advanced Filters (if shown) */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-6 p-6 bg-gray-50 rounded-xl"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Taxa Mínima
                </label>
                <input
                  type="range"
                  min="0"
                  max="20"
                  step="0.5"
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Apenas Verificadas
                </label>
                <input type="checkbox" className="rounded" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Apenas Promocionais
                </label>
                <input type="checkbox" className="rounded" />
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Results Count */}
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
        viewport={{ once: true }}
        className="mb-8"
      >
        <p className="text-gray-600">
          Mostrando <span className="font-semibold">{filteredRates.length}</span> resultados
          {selectedCategory !== 'Todas' && (
            <span> em <span className="font-semibold">{selectedCategory}</span></span>
          )}
        </p>
      </motion.div>

      {/* Cashback Rate Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredRates.map((comparison, index) => (
          <motion.div
            key={comparison.storeId}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
          >
            <CashbackRateCard
              comparison={comparison}
              featured={comparison.store.featured}
            />
          </motion.div>
        ))}
      </div>

      {/* Load More Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        viewport={{ once: true }}
        className="text-center mt-12"
      >
        <Button size="lg" className="px-8">
          Ver Todas as Lojas
        </Button>
      </motion.div>
    </Section>
  )
}
