'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  ChartBarIcon,
  ClockIcon,
  CpuChipIcon,
  SignalIcon,
} from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardContent } from '@/ui'

interface PerformanceMetrics {
  lcp: number
  fid: number
  cls: number
  ttfb: number
  fcp: number
  loadTime: number
  domNodes: number
  memoryUsage?: number
}

interface PerformanceDashboardProps {
  showInProduction?: boolean
}

export function PerformanceDashboard({ showInProduction = false }: PerformanceDashboardProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  // Only show in development or when explicitly enabled
  const shouldShow = process.env.NODE_ENV === 'development' || showInProduction

  useEffect(() => {
    if (!shouldShow) return

    const collectMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const paint = performance.getEntriesByType('paint')
      
      const fcp = paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
      
      const newMetrics: PerformanceMetrics = {
        lcp: 0, // Will be updated by observer
        fid: 0, // Will be updated by observer
        cls: 0, // Will be updated by observer
        ttfb: navigation.responseStart - navigation.requestStart,
        fcp,
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domNodes: document.querySelectorAll('*').length,
        memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
      }

      setMetrics(newMetrics)
    }

    // Collect initial metrics
    if (document.readyState === 'complete') {
      collectMetrics()
    } else {
      window.addEventListener('load', collectMetrics)
    }

    // Observe Core Web Vitals
    if ('PerformanceObserver' in window) {
      // LCP Observer
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        setMetrics(prev => prev ? { ...prev, lcp: lastEntry.startTime } : null)
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

      // FID Observer
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          const fid = entry.processingStart - entry.startTime
          setMetrics(prev => prev ? { ...prev, fid } : null)
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })

      // CLS Observer
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        setMetrics(prev => prev ? { ...prev, cls: clsValue } : null)
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })

      return () => {
        lcpObserver.disconnect()
        fidObserver.disconnect()
        clsObserver.disconnect()
      }
    }

    return () => {
      window.removeEventListener('load', collectMetrics)
    }
  }, [shouldShow])

  if (!shouldShow || !metrics) return null

  const getScoreColor = (value: number, thresholds: [number, number]) => {
    if (value <= thresholds[0]) return 'text-green-600'
    if (value <= thresholds[1]) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreLabel = (value: number, thresholds: [number, number]) => {
    if (value <= thresholds[0]) return 'Bom'
    if (value <= thresholds[1]) return 'Precisa Melhorar'
    return 'Ruim'
  }

  const performanceData = [
    {
      name: 'LCP',
      value: metrics.lcp,
      unit: 'ms',
      description: 'Largest Contentful Paint',
      thresholds: [2500, 4000] as [number, number],
      icon: ChartBarIcon,
    },
    {
      name: 'FID',
      value: metrics.fid,
      unit: 'ms',
      description: 'First Input Delay',
      thresholds: [100, 300] as [number, number],
      icon: ClockIcon,
    },
    {
      name: 'CLS',
      value: metrics.cls,
      unit: '',
      description: 'Cumulative Layout Shift',
      thresholds: [0.1, 0.25] as [number, number],
      icon: SignalIcon,
    },
    {
      name: 'TTFB',
      value: metrics.ttfb,
      unit: 'ms',
      description: 'Time to First Byte',
      thresholds: [800, 1800] as [number, number],
      icon: CpuChipIcon,
    },
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-4 right-4 z-50"
    >
      <div className="relative">
        <button
          onClick={() => setIsVisible(!isVisible)}
          className="bg-emerald-600 text-white p-3 rounded-full shadow-lg hover:bg-emerald-700 transition-colors"
          title="Performance Dashboard"
        >
          <ChartBarIcon className="w-6 h-6" />
        </button>

        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            className="absolute bottom-16 right-0 w-80"
          >
            <Card className="bg-white shadow-xl border">
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <ChartBarIcon className="w-5 h-5 mr-2 text-emerald-600" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  {performanceData.map((metric) => {
                    const Icon = metric.icon
                    const scoreColor = getScoreColor(metric.value, metric.thresholds)
                    const scoreLabel = getScoreLabel(metric.value, metric.thresholds)
                    
                    return (
                      <div key={metric.name} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Icon className="w-4 h-4 text-gray-500" />
                          <div>
                            <div className="font-medium text-sm">{metric.name}</div>
                            <div className="text-xs text-gray-500">{metric.description}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`font-bold ${scoreColor}`}>
                            {metric.value.toFixed(metric.name === 'CLS' ? 3 : 0)}{metric.unit}
                          </div>
                          <div className={`text-xs ${scoreColor}`}>
                            {scoreLabel}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                  
                  {/* Additional Metrics */}
                  <div className="pt-4 border-t border-gray-200 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">FCP</span>
                      <span className="font-medium">{metrics.fcp.toFixed(0)}ms</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Load Time</span>
                      <span className="font-medium">{metrics.loadTime.toFixed(0)}ms</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">DOM Nodes</span>
                      <span className="font-medium">{metrics.domNodes}</span>
                    </div>
                    {metrics.memoryUsage && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Memory</span>
                        <span className="font-medium">
                          {(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}
