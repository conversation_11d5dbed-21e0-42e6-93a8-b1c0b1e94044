'use client'

import { motion } from 'framer-motion'
import { 
  ChartBarIcon,
  StarIcon,
  CheckIcon,
  XMarkIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

const platforms = [
  {
    id: 'meliuz',
    name: '<PERSON><PERSON><PERSON>',
    logo: '/logos/meliuz.svg',
    rating: 4.7,
    totalStores: 2800,
    averageCashback: 4.1,
    minPayout: 20.00,
    payoutTime: '2-3 dias',
    payoutMethods: ['PIX', 'Bank Transfer', 'Gift Cards'],
    signupBonus: 15,
    referralBonus: 20,
    features: {
      browserExtension: true,
      mobileApp: true,
      priceComparison: true,
      couponCodes: true,
      inStoreOffers: true,
      customerSupport: 'Excellent',
    },
    pros: ['Leading Brazilian platform', 'PIX payments', 'High cashback rates'],
    cons: ['Brazil-focused stores', 'Higher minimum payout'],
    color: 'from-purple-500 to-indigo-500',
  },
  {
    id: 'intershopping',
    name: 'Inter Shopping',
    logo: '/logos/intershopping.svg',
    rating: 4.6,
    totalStores: 1800,
    averageCashback: 3.5,
    minPayout: 10.00,
    payoutTime: '1-2 dias',
    payoutMethods: ['PIX', 'Inter Account', 'Bank Transfer'],
    signupBonus: 10,
    referralBonus: 15,
    features: {
      browserExtension: true,
      mobileApp: true,
      priceComparison: false,
      couponCodes: true,
      inStoreOffers: true,
      customerSupport: 'Good',
    },
    pros: ['Instant PIX payments', 'Low minimum payout', 'Bank integration'],
    cons: ['Smaller store selection', 'Brazil-only focus'],
    color: 'from-emerald-500 to-green-500',
  },
  {
    id: 'rakuten',
    name: 'Rakuten',
    logo: '/logos/rakuten.svg',
    rating: 4.8,
    totalStores: 3500,
    averageCashback: 3.2,
    minPayout: 5.01,
    payoutTime: '3-5 dias',
    payoutMethods: ['PayPal', 'Check'],
    signupBonus: 10,
    referralBonus: 25,
    features: {
      browserExtension: true,
      mobileApp: true,
      priceComparison: true,
      couponCodes: true,
      inStoreOffers: true,
      customerSupport: 'Excellent',
    },
    pros: ['Largest international network', 'Reliable payouts', 'Great browser extension'],
    cons: ['USD payments only', 'Limited Brazilian stores'],
    color: 'from-orange-500 to-yellow-500',
  },
  {
    id: 'bancopan',
    name: 'Banco Pan',
    logo: '/logos/bancopan.svg',
    rating: 4.4,
    totalStores: 1500,
    averageCashback: 2.8,
    minPayout: 25,
    payoutTime: '1-2 dias',
    payoutMethods: ['PIX', 'Pan Account', 'Bank Transfer'],
    signupBonus: 20,
    referralBonus: 15,
    features: {
      browserExtension: false,
      mobileApp: true,
      priceComparison: false,
      couponCodes: true,
      inStoreOffers: true,
      customerSupport: 'Good',
    },
    pros: ['Bank integration', 'Fast PIX payments', 'Good signup bonus'],
    cons: ['No browser extension', 'Limited store selection'],
    color: 'from-blue-500 to-cyan-500',
  },
]

const comparisonFeatures = [
  { name: 'Extensão do Navegador', key: 'browserExtension' },
  { name: 'App Mobile', key: 'mobileApp' },
  { name: 'Comparação de Preços', key: 'priceComparison' },
  { name: 'Códigos de Cupom', key: 'couponCodes' },
  { name: 'Ofertas na Loja', key: 'inStoreOffers' },
]

export default function PlatformComparisonSection() {
  const renderStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <StarIconSolid key={i} className="w-4 h-4 text-yellow-400" />
      )
    }

    if (hasHalfStar) {
      stars.push(
        <div key="half" className="relative">
          <StarIcon className="w-4 h-4 text-gray-300" />
          <div className="absolute inset-0 overflow-hidden w-1/2">
            <StarIconSolid className="w-4 h-4 text-yellow-400" />
          </div>
        </div>
      )
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <StarIcon key={`empty-${i}`} className="w-4 h-4 text-gray-300" />
      )
    }

    return stars
  }

  return (
    <section id="platforms" className="py-responsive bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container-responsive">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 rounded-full bg-white text-gray-700 font-medium text-sm mb-6 shadow-sm"
          >
            <ChartBarIcon className="w-4 h-4 mr-2" />
            Comparação de Plataformas
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-responsive-2xl font-poppins font-bold text-gray-900 mb-4"
          >
            Comparação de Plataformas{' '}
            <span className="gradient-text">Brasileiras e Internacionais</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-responsive-sm text-gray-600 max-w-2xl mx-auto"
          >
            Compare recursos, métodos de pagamento e taxas médias entre plataformas de cashback brasileiras e internacionais.
            Veja qual plataforma funciona melhor para seus hábitos de compra.
          </motion.p>
        </div>

        {/* Platform Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8 mb-16">
          {platforms.map((platform, index) => (
            <motion.div
              key={platform.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              {/* Platform Header */}
              <div className="text-center mb-6">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${platform.color} shadow-lg mb-4`}>
                  <span className="text-white font-bold text-xl">
                    {platform.name.charAt(0)}
                  </span>
                </div>
                <h3 className="text-xl font-poppins font-bold text-gray-900 mb-2">
                  {platform.name}
                </h3>
                <div className="flex items-center justify-center space-x-1 mb-1">
                  {renderStars(platform.rating)}
                </div>
                <span className="text-sm text-gray-600 font-medium">
                  {platform.rating} avaliação
                </span>
              </div>

              {/* Key Stats */}
              <div className="space-y-4 mb-6">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Lojas</span>
                  <span className="font-semibold text-gray-900">
                    {platform.totalStores.toLocaleString()}+
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Cashback Médio</span>
                  <span className="font-semibold text-emerald-600">
                    {platform.averageCashback}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Saque Mínimo</span>
                  <span className="font-semibold text-gray-900">
                    R${platform.minPayout}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Tempo de Saque</span>
                  <span className="font-semibold text-gray-900">
                    {platform.payoutTime}
                  </span>
                </div>
              </div>

              {/* Features */}
              <div className="mb-6">
                <h4 className="text-sm font-semibold text-gray-700 mb-3">Recursos</h4>
                <div className="space-y-2">
                  {comparisonFeatures.map((feature) => (
                    <div key={feature.key} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{feature.name}</span>
                      {platform.features[feature.key as keyof typeof platform.features] ? (
                        <CheckIcon className="w-4 h-4 text-emerald-500" />
                      ) : (
                        <XMarkIcon className="w-4 h-4 text-gray-300" />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Bonuses */}
              {(platform.signupBonus > 0 || platform.referralBonus > 0) && (
                <div className="bg-emerald-50 rounded-xl p-4 mb-6">
                  <h4 className="text-sm font-semibold text-emerald-700 mb-2">Bônus</h4>
                  {platform.signupBonus > 0 && (
                    <div className="text-sm text-emerald-600 mb-1">
                      R${platform.signupBonus} bônus de cadastro
                    </div>
                  )}
                  {platform.referralBonus > 0 && (
                    <div className="text-sm text-emerald-600">
                      R${platform.referralBonus} bônus de indicação
                    </div>
                  )}
                </div>
              )}

              {/* CTA */}
              <button className="w-full btn-primary justify-center group-hover:shadow-green-strong transition-all duration-200">
                Comparar Taxas
              </button>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center bg-white rounded-3xl p-8 shadow-lg"
        >
          <h3 className="text-2xl font-poppins font-bold text-gray-900 mb-4">
            Todas as Plataformas, Uma Comparação
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Veja taxas de plataformas brasileiras como Meliuz e Inter Shopping ao lado de
            opções internacionais como Rakuten - tudo em um lugar, instantaneamente.
          </p>
          <button className="btn-primary text-lg px-8 py-4">
            Compare Todas as Taxas Agora
          </button>
        </motion.div>
      </div>
    </section>
  )
}
