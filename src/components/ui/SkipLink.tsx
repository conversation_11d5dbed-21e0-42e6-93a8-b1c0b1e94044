'use client'

import { cn } from '@/lib/utils'

interface SkipLinkProps {
  href: string
  children: React.ReactNode
  className?: string
}

/**
 * Skip link component for keyboard navigation accessibility
 */
export function SkipLink({ href, children, className }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50',
        'bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium',
        'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2',
        'transition-all duration-200',
        className
      )}
    >
      {children}
    </a>
  )
}

/**
 * Skip navigation component with multiple skip links
 */
export function SkipNavigation() {
  return (
    <div className="sr-only focus-within:not-sr-only">
      <SkipLink href="#main-content">
        Pular para o conteúdo principal
      </SkipLink>
      <SkipLink href="#navigation" className="ml-2">
        Pular para a navegação
      </SkipLink>
      <SkipLink href="#search" className="ml-2">
        Pular para a busca
      </SkipLink>
      <SkipLink href="#footer" className="ml-2">
        Pular para o rodapé
      </SkipLink>
    </div>
  )
}
