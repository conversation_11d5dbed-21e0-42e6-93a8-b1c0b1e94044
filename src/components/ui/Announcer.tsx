'use client'

import { useEffect, useState } from 'react'

interface AnnouncerProps {
  message: string
  priority?: 'polite' | 'assertive'
  delay?: number
}

/**
 * Component for announcing messages to screen readers
 */
export function Announcer({ 
  message, 
  priority = 'polite', 
  delay = 0 
}: AnnouncerProps) {
  const [announcement, setAnnouncement] = useState('')

  useEffect(() => {
    if (!message) return

    const timer = setTimeout(() => {
      setAnnouncement(message)
      
      // Clear the announcement after it's been read
      const clearTimer = setTimeout(() => {
        setAnnouncement('')
      }, 1000)

      return () => clearTimeout(clearTimer)
    }, delay)

    return () => clearTimeout(timer)
  }, [message, delay])

  if (!announcement) return null

  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    >
      {announcement}
    </div>
  )
}

/**
 * Hook for programmatic announcements
 */
export function useAnnouncer() {
  const [announcements, setAnnouncements] = useState<Array<{
    id: string
    message: string
    priority: 'polite' | 'assertive'
  }>>([])

  const announce = (
    message: string, 
    priority: 'polite' | 'assertive' = 'polite'
  ) => {
    const id = Math.random().toString(36).substr(2, 9)
    
    setAnnouncements(prev => [...prev, { id, message, priority }])

    // Remove announcement after delay
    setTimeout(() => {
      setAnnouncements(prev => prev.filter(a => a.id !== id))
    }, 1000)
  }

  const AnnouncerComponent = () => (
    <>
      {announcements.map(({ id, message, priority }) => (
        <Announcer
          key={id}
          message={message}
          priority={priority}
        />
      ))}
    </>
  )

  return {
    announce,
    AnnouncerComponent,
  }
}
