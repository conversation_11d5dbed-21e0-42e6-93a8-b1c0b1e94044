'use client'

import { memo } from 'react'
import { motion } from 'framer-motion'
import { 
  SunIcon, 
  MoonIcon, 
  ComputerDesktopIcon 
} from '@heroicons/react/24/outline'
import { useTheme } from '@/hooks/useTheme'
import { cn } from '@/lib/utils'

const themeIcons = {
  light: SunIcon,
  dark: MoonIcon,
  system: ComputerDesktopIcon,
}

const themeLabels = {
  light: 'Claro',
  dark: 'Escuro',
  system: 'Sistema',
}

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown'
  className?: string
}

export const ThemeToggle = memo(({ 
  variant = 'button', 
  className 
}: ThemeToggleProps) => {
  const { theme, toggleTheme, setTheme } = useTheme()

  if (variant === 'dropdown') {
    return (
      <div className={cn('relative', className)}>
        <select
          value={theme}
          onChange={(e) => setTheme(e.target.value as any)}
          className="appearance-none bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
        >
          {Object.entries(themeLabels).map(([value, label]) => (
            <option key={value} value={value}>
              {label}
            </option>
          ))}
        </select>
      </div>
    )
  }

  const Icon = themeIcons[theme]

  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={toggleTheme}
      className={cn(
        'inline-flex items-center justify-center w-10 h-10 rounded-lg',
        'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
        'text-gray-700 dark:text-gray-300 hover:text-emerald-600 dark:hover:text-emerald-400',
        'transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500',
        className
      )}
      aria-label={`Alternar tema (atual: ${themeLabels[theme]})`}
      title={`Tema atual: ${themeLabels[theme]}`}
    >
      <Icon className="w-5 h-5" />
    </motion.button>
  )
})

ThemeToggle.displayName = 'ThemeToggle'
