'use client'

import React, { Suspense, lazy, ComponentType } from 'react'
import { Skeleton } from './Skeleton'
import { ErrorBoundary } from './ErrorBoundary'

interface LazyLoadProps {
  fallback?: React.ReactNode
  errorFallback?: React.ReactNode
  children: React.ReactNode
}

/**
 * Wrapper component for lazy loading with error boundary and loading state
 */
export function LazyLoad({ 
  fallback = <Skeleton variant="card" />, 
  errorFallback,
  children 
}: LazyLoadProps) {
  return (
    <ErrorBoundary fallback={errorFallback}>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </ErrorBoundary>
  )
}

/**
 * Higher-order component for creating lazy-loaded components
 */
export function withLazyLoading<P extends object>(
  importFunc: () => Promise<{ default: ComponentType<P> }>,
  fallback?: React.ReactNode
) {
  const LazyComponent = lazy(importFunc)
  
  return function LazyLoadedComponent(props: P) {
    return (
      <LazyLoad fallback={fallback}>
        <LazyComponent {...props} />
      </LazyLoad>
    )
  }
}

/**
 * Lazy load components based on intersection observer
 */
export function LazySection({ 
  children, 
  fallback = <Skeleton variant="card" className="h-96" />,
  threshold = 0.1,
  rootMargin = '50px'
}: {
  children: React.ReactNode
  fallback?: React.ReactNode
  threshold?: number
  rootMargin?: string
}) {
  const [isVisible, setIsVisible] = React.useState(false)
  const ref = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold, rootMargin }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [threshold, rootMargin])

  return (
    <div ref={ref}>
      {isVisible ? children : fallback}
    </div>
  )
}
