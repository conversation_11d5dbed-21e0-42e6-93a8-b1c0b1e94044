'use client'

import { motion } from 'framer-motion'
import { 
  MagnifyingGlassIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'

const steps = [
  {
    id: 1,
    title: 'Busque e Compare',
    description: 'Digite o nome de qualquer loja ou navegue por categoria para ver taxas de cashback em tempo real de todas as principais plataformas.',
    icon: MagnifyingGlassIcon,
    color: 'from-blue-500 to-cyan-500',
    features: ['500+ lojas', 'Taxas em tempo real', 'Todas as plataformas'],
  },
  {
    id: 2,
    title: 'Encontre as Melhores Taxas',
    description: 'Nosso mecanismo de comparação inteligente mostra instantaneamente qual plataforma oferece o maior cashback para sua compra.',
    icon: ChartBarIcon,
    color: 'from-emerald-500 to-green-500',
    features: ['Comparação inteligente', 'Análise de tendências', 'Alertas de taxa'],
  },
  {
    id: 3,
    title: 'Comece a Ganhar',
    description: 'Clique na plataforma escolhida e comece a ganhar o máximo de cashback em cada compra que fizer.',
    icon: CurrencyDollarIcon,
    color: 'from-yellow-500 to-orange-500',
    features: ['Ganhos máximos', 'Rastreamento instantâneo', 'Pagamentos fáceis'],
  },
]

const benefits = [
  'Economize tempo comparando taxas manualmente',
  'Nunca perca as melhores ofertas de cashback',
  'Maximize os ganhos em cada compra',
  'Mantenha-se atualizado com mudanças de taxa',
  'Acesse bônus exclusivos das plataformas',
  'Acompanhe suas economias totais',
]

export default function HowItWorksSection() {
  return (
    <section id="how-it-works" className="py-responsive bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50">
      <div className="container-responsive">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm text-emerald-700 font-medium text-sm mb-6 shadow-sm"
          >
            <SparklesIcon className="w-4 h-4 mr-2" />
            Processo Simples de 3 Passos
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-responsive-2xl font-poppins font-bold text-gray-900 mb-4"
          >
            Como o{' '}
            <span className="gradient-text">CashBoost</span>
            {' '}Funciona
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-responsive-sm text-gray-600 max-w-2xl mx-auto"
          >
            Pare de perder tempo verificando vários sites de cashback. Nossa plataforma faz o trabalho pesado,
            para que você possa focar no que mais importa - economizar dinheiro.
          </motion.p>
        </div>

        {/* Steps */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 mb-16">
          {steps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Connection Line (Desktop) */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-16 left-full w-12 h-0.5 bg-gradient-to-r from-emerald-200 to-emerald-300 z-0" />
              )}

              <div className="relative z-10 text-center">
                {/* Icon */}
                <motion.div
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                  className={`inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br ${step.color} shadow-lg mb-6 group cursor-pointer`}
                >
                  <step.icon className="w-10 h-10 text-white" />
                </motion.div>

                {/* Step Number */}
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center">
                  <span className="text-sm font-bold text-emerald-600">
                    {step.id}
                  </span>
                </div>

                {/* Content */}
                <h3 className="text-xl font-poppins font-bold text-gray-900 mb-4">
                  {step.title}
                </h3>
                
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {step.description}
                </p>

                {/* Features */}
                <div className="space-y-2">
                  {step.features.map((feature, featureIndex) => (
                    <motion.div
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: (index * 0.2) + (featureIndex * 0.1) }}
                      viewport={{ once: true }}
                      className="inline-flex items-center px-3 py-1 bg-white/60 backdrop-blur-sm rounded-full text-sm font-medium text-emerald-700 mr-2 mb-2"
                    >
                      <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-2" />
                      {feature}
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Benefits Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-12 shadow-xl"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-poppins font-bold text-gray-900 mb-4">
              Por que Escolher o CashBoost?
            </h3>
            <p className="text-gray-600">
              Junte-se a milhares de compradores inteligentes que já estão maximizando seus ganhos de cashback
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center space-x-3 p-4 rounded-xl hover:bg-emerald-50/50 transition-colors duration-200"
              >
                <div className="flex-shrink-0 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-gray-700 font-medium">
                  {benefit}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <button className="btn-primary text-lg px-8 py-4 animate-shine">
            Comece a Comparar Taxas Agora
          </button>
          <p className="text-sm text-gray-500 mt-4">
            Gratuito para usar • Sem cadastro necessário • Resultados instantâneos
          </p>
        </motion.div>
      </div>
    </section>
  )
}
