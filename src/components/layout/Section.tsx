import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Container } from './Container'

interface SectionProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'default' | 'gradient' | 'dark'
  size?: 'sm' | 'md' | 'lg'
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  animate?: boolean
  children: React.ReactNode
}

const sectionVariants = {
  default: 'bg-white',
  gradient: 'bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50',
  dark: 'bg-gray-900 text-white',
}

const sectionSizes = {
  sm: 'py-8 md:py-12',
  md: 'py-12 md:py-16 lg:py-20',
  lg: 'py-16 md:py-20 lg:py-24',
}

export function Section({
  variant = 'default',
  size = 'md',
  containerSize = 'xl',
  animate = true,
  className,
  children,
  ...props
}: SectionProps) {
  const content = (
    <Container size={containerSize}>
      {children}
    </Container>
  )

  if (animate) {
    return (
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className={cn(
          sectionVariants[variant],
          sectionSizes[size],
          className
        )}
        {...props}
      >
        {content}
      </motion.section>
    )
  }

  return (
    <section
      className={cn(
        sectionVariants[variant],
        sectionSizes[size],
        className
      )}
      {...props}
    >
      {content}
    </section>
  )
}
