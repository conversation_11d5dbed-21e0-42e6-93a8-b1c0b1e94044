'use client'

import { motion } from 'framer-motion'
import { 
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline'
import { Container } from './Container'
import { Button } from '@/ui'
import { scrollToElement } from '@/lib/utils'
import { CONTACT_INFO, SOCIAL_LINKS } from '@/constants'

const footerLinks = {
  platform: {
    title: 'Plataforma',
    links: [
      { name: 'Como Funciona', href: '#how-it-works' },
      { name: 'Comparar <PERSON>', href: '#rates' },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '#stores' },
      { name: 'Todas as Plataformas', href: '#platforms' },
      { name: 'Ofertas Especiais', href: '#offers' },
    ],
  },
  resources: {
    title: 'Recursos',
    links: [
      { name: 'Blog', href: '/blog' },
      { name: '<PERSON><PERSON><PERSON>', href: '/guide' },
      { name: '<PERSON><PERSON>as de Taxa', href: '/alerts' },
      { name: 'Documentação da API', href: '/api' },
      { name: 'Central de Ajuda', href: '/help' },
    ],
  },
  company: {
    title: 'Empresa',
    links: [
      { name: 'Sobre Nós', href: '/about' },
      { name: 'Carreiras', href: '/careers' },
      { name: 'Imprensa', href: '/press' },
      { name: 'Parcerias', href: '/partnerships' },
      { name: 'Contato', href: '/contact' },
    ],
  },
  legal: {
    title: 'Legal',
    links: [
      { name: 'Política de Privacidade', href: '/privacy' },
      { name: 'Termos de Uso', href: '/terms' },
      { name: 'Política de Cookies', href: '/cookies' },
      { name: 'LGPD', href: '/lgpd' },
      { name: 'Disclaimer', href: '/disclaimer' },
    ],
  },
} as const

const socialIcons = {
  twitter: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
    </svg>
  ),
  instagram: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path fillRule="evenodd" d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.229 14.794 3.74 13.643 3.74 12.346s.49-2.448 1.386-3.323c.896-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.875-2.026 1.297-3.323 1.297z" clipRule="evenodd" />
    </svg>
  ),
  linkedin: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
    </svg>
  ),
}

export function Footer() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleLinkClick = (href: string) => {
    if (href.startsWith('#')) {
      scrollToElement(href.substring(1))
    }
  }

  return (
    <footer className="bg-gray-900 text-white">
      <Container>
        <div className="py-16">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12">
            {/* Company Info */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                {/* Logo */}
                <div className="flex items-center space-x-2 mb-6">
                  <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-green-medium">
                    <span className="text-white font-bold text-xl">₵</span>
                  </div>
                  <span className="font-poppins font-bold text-2xl text-white">
                    CashBoost
                  </span>
                </div>

                <p className="text-gray-300 mb-6 leading-relaxed">
                  A plataforma mais completa para comparar taxas de cashback no Brasil. 
                  Maximize suas economias e nunca perca as melhores ofertas.
                </p>

                {/* Contact Info */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 text-gray-300">
                    <EnvelopeIcon className="w-5 h-5 text-emerald-400" />
                    <span>{CONTACT_INFO.email}</span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-300">
                    <PhoneIcon className="w-5 h-5 text-emerald-400" />
                    <span>{CONTACT_INFO.phone}</span>
                  </div>
                  <div className="flex items-center space-x-3 text-gray-300">
                    <MapPinIcon className="w-5 h-5 text-emerald-400" />
                    <span>{CONTACT_INFO.address}</span>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Footer Links */}
            {Object.entries(footerLinks).map(([key, section], index) => (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="font-poppins font-bold text-white mb-4">
                  {section.title}
                </h3>
                <ul className="space-y-3">
                  {section.links.map((link) => (
                    <li key={link.name}>
                      <button
                        onClick={() => handleLinkClick(link.href)}
                        className="text-gray-300 hover:text-emerald-400 transition-colors duration-200 text-left"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>

          {/* Newsletter Signup */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-16 pt-8 border-t border-gray-800"
          >
            <div className="max-w-md mx-auto text-center lg:text-left lg:max-w-none lg:flex lg:items-center lg:justify-between">
              <div className="lg:flex-1">
                <h3 className="font-poppins font-bold text-xl text-white mb-2">
                  Receba as Melhores Ofertas
                </h3>
                <p className="text-gray-300">
                  Seja notificado sobre aumentos de taxa e ofertas exclusivas
                </p>
              </div>
              <div className="mt-6 lg:mt-0 lg:ml-8">
                <div className="flex flex-col sm:flex-row gap-3">
                  <input
                    type="email"
                    placeholder="Seu melhor e-mail"
                    className="flex-1 px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                  <Button className="whitespace-nowrap">
                    Inscrever-se
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Footer */}
        <div className="py-8 border-t border-gray-800">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
              <p className="text-gray-400 text-sm">
                © 2024 CashBoost. Todos os direitos reservados.
              </p>
              
              {/* Social Links */}
              <div className="flex space-x-4">
                {Object.entries(socialIcons).map(([platform, icon]) => (
                  <a
                    key={platform}
                    href={SOCIAL_LINKS[platform as keyof typeof SOCIAL_LINKS]}
                    className="text-gray-400 hover:text-emerald-400 transition-colors duration-200"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={`Seguir no ${platform}`}
                  >
                    {icon}
                  </a>
                ))}
              </div>
            </div>

            {/* Back to Top */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={scrollToTop}
              className="mt-6 lg:mt-0 inline-flex items-center space-x-2 text-gray-400 hover:text-emerald-400 transition-colors duration-200"
            >
              <span className="text-sm">Voltar ao Topo</span>
              <ArrowUpIcon className="w-4 h-4" />
            </motion.button>
          </div>
        </div>
      </Container>
    </footer>
  )
}
