'use client'

import { motion } from 'framer-motion'
import { 
  ShieldCheckIcon,
  UsersIcon,
  CurrencyDollarIcon,
  StarIcon,
  HeartIcon,
  TrophyIcon
} from '@heroicons/react/24/outline'

const trustStats = [
  {
    id: 1,
    value: '2.1M+',
    label: 'Usuários Satisfeitos',
    description: 'Compradores confiam no CashBoost para suas necessidades de cashback',
    icon: UsersIcon,
    color: 'from-blue-500 to-cyan-500',
  },
  {
    id: 2,
    value: 'R$52M+',
    label: 'Economia Total',
    description: 'Cashback ganho pelos membros da nossa comunidade',
    icon: CurrencyDollarIcon,
    color: 'from-emerald-500 to-green-500',
  },
  {
    id: 3,
    value: '500+',
    label: 'Lojas Parceiras',
    description: 'Principais marcas e varejistas em nossa rede',
    icon: ShieldCheckIcon,
    color: 'from-purple-500 to-indigo-500',
  },
  {
    id: 4,
    value: '4.9/5',
    label: 'Avaliação dos Usuários',
    description: 'Avaliação média de usuários verificados',
    icon: StarIcon,
    color: 'from-yellow-500 to-orange-500',
  },
]

const testimonials = [
  {
    id: 1,
    name: 'Ana Silva',
    role: 'Compradora Online Frequente',
    avatar: '/avatars/ana.jpg',
    rating: 5,
    text: "O CashBoost mudou completamente como eu compro online. Economizei mais de R$800 este ano apenas comparando taxas antes de fazer compras. A interface é muito limpa e fácil de usar!",
    savings: 'R$847',
    timeUsing: '8 meses',
  },
  {
    id: 2,
    name: 'Carlos Santos',
    role: 'Entusiasta de Tecnologia',
    avatar: '/avatars/carlos.jpg',
    rating: 5,
    text: "Como alguém que compra muitos eletrônicos, encontrar as melhores taxas de cashback sempre foi um problema. O CashBoost torna isso sem esforço. Gostaria de ter encontrado esta plataforma antes!",
    savings: 'R$1.240',
    timeUsing: '1 ano',
  },
  {
    id: 3,
    name: 'Mariana Costa',
    role: 'Blogueira de Moda',
    avatar: '/avatars/mariana.jpg',
    rating: 5,
    text: "As atualizações de taxa em tempo real são incríveis. Peguei uma taxa de cashback de 12% na Nike que durou apenas algumas horas. Os alertas do CashBoost me salvaram de perder ofertas incríveis.",
    savings: 'R$623',
    timeUsing: '6 meses',
  },
]

const features = [
  {
    title: 'Atualizações em Tempo Real',
    description: 'Taxas de cashback atualizadas a cada hora',
    icon: '⚡',
  },
  {
    title: 'Sem Taxas Ocultas',
    description: 'Completamente gratuito para usar, sempre',
    icon: '💯',
  },
  {
    title: 'Taxas Verificadas',
    description: 'Todas as taxas verificadas e precisas',
    icon: '✅',
  },
  {
    title: 'Privacidade em Primeiro Lugar',
    description: 'Seus dados permanecem privados e seguros',
    icon: '🔒',
  },
]

export default function TrustSection() {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  return (
    <section className="py-responsive bg-white">
      <div className="container-responsive">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center px-4 py-2 rounded-full bg-emerald-50 text-emerald-700 font-medium text-sm mb-6"
          >
            <HeartIcon className="w-4 h-4 mr-2" />
            Confiado por Milhões
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-responsive-2xl font-poppins font-bold text-gray-900 mb-4"
          >
            Junte-se à Comunidade de{' '}
            <span className="gradient-text">Compradores Inteligentes</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-responsive-sm text-gray-600 max-w-2xl mx-auto"
          >
            Milhares de compradores já descobriram o poder da comparação inteligente de cashback.
            Veja o que eles estão dizendo sobre o CashBoost.
          </motion.p>
        </div>

        {/* Trust Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {trustStats.map((stat, index) => (
            <motion.div
              key={stat.id}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center group"
            >
              <motion.div
                whileHover={{ scale: 1.05, rotate: 5 }}
                transition={{ duration: 0.2 }}
                className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${stat.color} shadow-lg mb-4 group-hover:shadow-xl transition-shadow duration-300`}
              >
                <stat.icon className="w-8 h-8 text-white" />
              </motion.div>
              
              <motion.div
                initial={{ scale: 0.8 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}
                viewport={{ once: true }}
                className="text-3xl lg:text-4xl font-poppins font-black text-gray-900 mb-2"
              >
                {stat.value}
              </motion.div>
              
              <h3 className="text-lg font-bold text-gray-900 mb-2">
                {stat.label}
              </h3>
              
              <p className="text-sm text-gray-600 leading-relaxed">
                {stat.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h3 className="text-2xl font-poppins font-bold text-gray-900 mb-4">
              O que Nossos Usuários Dizem
            </h3>
            <p className="text-gray-600">
              Histórias reais de usuários reais que estão economizando mais com o CashBoost
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
              >
                {/* User Info */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center shadow-md">
                    <span className="text-white font-bold text-lg">
                      {testimonial.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {testimonial.role}
                    </p>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center space-x-1 mb-4">
                  {renderStars(testimonial.rating)}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-gray-700 leading-relaxed mb-6 italic">
                  "{testimonial.text}"
                </blockquote>

                {/* Stats */}
                <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                  <div className="text-center">
                    <div className="text-lg font-bold text-emerald-600">
                      {testimonial.savings}
                    </div>
                    <div className="text-xs text-gray-500">
                      Total Economizado
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">
                      {testimonial.timeUsing}
                    </div>
                    <div className="text-xs text-gray-500">
                      Usando CashBoost
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-3xl p-8 lg:p-12"
        >
          <div className="text-center mb-8">
            <TrophyIcon className="w-12 h-12 text-emerald-600 mx-auto mb-4" />
            <h3 className="text-2xl font-poppins font-bold text-gray-900 mb-4">
              Por que o CashBoost se Destaca
            </h3>
            <p className="text-gray-600">
              Estamos comprometidos em fornecer a melhor experiência de comparação de cashback
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl hover:bg-white/80 transition-all duration-200"
              >
                <div className="text-3xl mb-3">
                  {feature.icon}
                </div>
                <h4 className="font-bold text-gray-900 mb-2">
                  {feature.title}
                </h4>
                <p className="text-sm text-gray-600">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
