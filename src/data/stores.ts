import type { Store } from '@/types'

export const STORES: Store[] = [
  {
    id: 'nike',
    name: 'Nike',
    slug: 'nike',
    logo: '/logos/nike.svg',
    logoAlt: 'Nike Logo',
    website: 'https://www.nike.com.br',
    description: 'Artigos esportivos e calçados da marca Nike',
    category: {
      id: 'fashion',
      name: 'Moda & Vestuário',
      slug: 'moda-vestuario',
      icon: '👕',
      description: 'Roupas, calçados e acessórios',
      storeCount: 150,
      averageCashback: 6.2,
      topStores: ['nike', 'adidas', 'zara'],
    },
    featured: true,
    verified: true,
    trustScore: 4.8,
    totalOffers: 25,
    lastUpdated: '2024-12-21T10:00:00Z',
    brandColors: {
      primary: '#000000',
      secondary: '#FFFFFF',
    },
  },
  {
    id: 'amazon',
    name: 'Amazon',
    slug: 'amazon',
    logo: '/logos/amazon.svg',
    logoAlt: 'Amazon Logo',
    website: 'https://www.amazon.com.br',
    description: 'Marketplace com milhões de produtos',
    category: {
      id: 'marketplace',
      name: 'Marketplace',
      slug: 'marketplace',
      icon: '🛒',
      description: 'Plataformas de venda online',
      storeCount: 50,
      averageCashback: 4.5,
      topStores: ['amazon', 'mercadolivre', 'americanas'],
    },
    featured: true,
    verified: true,
    trustScore: 4.9,
    totalOffers: 45,
    lastUpdated: '2024-12-21T09:30:00Z',
    brandColors: {
      primary: '#FF9900',
      secondary: '#232F3E',
    },
  },
  {
    id: 'magazine-luiza',
    name: 'Magazine Luiza',
    slug: 'magazine-luiza',
    logo: '/logos/magalu.svg',
    logoAlt: 'Magazine Luiza Logo',
    website: 'https://www.magazineluiza.com.br',
    description: 'Varejo brasileiro com produtos eletrônicos e casa',
    category: {
      id: 'electronics',
      name: 'Eletrônicos',
      slug: 'eletronicos',
      icon: '📱',
      description: 'Eletrônicos e tecnologia',
      storeCount: 80,
      averageCashback: 5.8,
      topStores: ['magazine-luiza', 'casas-bahia', 'extra'],
    },
    featured: true,
    verified: true,
    trustScore: 4.7,
    totalOffers: 35,
    lastUpdated: '2024-12-21T08:45:00Z',
    brandColors: {
      primary: '#0066CC',
      secondary: '#FF6B35',
    },
  },
  {
    id: 'americanas',
    name: 'Americanas',
    slug: 'americanas',
    logo: '/logos/americanas.svg',
    logoAlt: 'Americanas Logo',
    website: 'https://www.americanas.com.br',
    description: 'Varejo brasileiro com produtos diversos',
    category: {
      id: 'marketplace',
      name: 'Marketplace',
      slug: 'marketplace',
      icon: '🛒',
      description: 'Plataformas de venda online',
      storeCount: 50,
      averageCashback: 4.5,
      topStores: ['amazon', 'mercadolivre', 'americanas'],
    },
    featured: true,
    verified: true,
    trustScore: 4.6,
    totalOffers: 30,
    lastUpdated: '2024-12-21T07:20:00Z',
    brandColors: {
      primary: '#E60014',
      secondary: '#FFFFFF',
    },
  },
  {
    id: 'casas-bahia',
    name: 'Casas Bahia',
    slug: 'casas-bahia',
    logo: '/logos/casas-bahia.svg',
    logoAlt: 'Casas Bahia Logo',
    website: 'https://www.casasbahia.com.br',
    description: 'Varejo de móveis, eletrodomésticos e eletrônicos',
    category: {
      id: 'home',
      name: 'Casa & Decoração',
      slug: 'casa-decoracao',
      icon: '🏠',
      description: 'Móveis, decoração e casa',
      storeCount: 60,
      averageCashback: 5.2,
      topStores: ['casas-bahia', 'tok-stok', 'leroy-merlin'],
    },
    featured: false,
    verified: true,
    trustScore: 4.5,
    totalOffers: 28,
    lastUpdated: '2024-12-21T06:15:00Z',
    brandColors: {
      primary: '#FF6B00',
      secondary: '#003366',
    },
  },
]
