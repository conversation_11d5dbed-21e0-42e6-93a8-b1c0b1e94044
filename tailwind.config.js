/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        inter: ['Inter', 'sans-serif'],
        poppins: ['Poppins', 'sans-serif'],
      },
      colors: {
        primary: {
          50: '#F0FDF4',
          100: '#DCFCE7',
          200: '#BBF7D0',
          300: '#86EFAC',
          400: '#4ADE80',
          500: '#10B981',
          600: '#059669',
          700: '#047857',
          800: '#065F46',
          900: '#064E3B',
        },
        secondary: {
          green: '#2ED573',
        },
        accent: {
          orange: '#F59E0B',
          gold: '#FCD34D',
          coral: '#FF6B6B',
          purple: '#8B5CF6',
          blue: '#3B82F6',
          cyan: '#06B6D4',
          red: '#EF4444',
        },
        background: {
          'primary-light': '#F0FDF4',
          'secondary-light': '#ECFDF5',
          'tertiary-mint': '#F0FDFA',
          'accent-yellow': '#FEF3C7',
          'glass-white': 'rgba(255, 255, 255, 0.25)',
          'glass-green': 'rgba(16, 185, 129, 0.1)',
        },
        text: {
          'primary-dark': '#1F2937',
          'secondary-medium': '#4B5563',
          'light': '#6B7280',
          'ultra-light': '#9CA3AF',
        },
      },
      backgroundImage: {
        'gradient-hero': 'linear-gradient(135deg, #10B981 0%, #34D399 50%, #6EE7B7 100%)',
        'gradient-card': 'linear-gradient(145deg, #FFFFFF 0%, #F0FDF4 100%)',
        'gradient-button': 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
      },
      boxShadow: {
        'soft-green': '0 4px 20px rgba(16, 185, 129, 0.15)',
        'medium-green': '0 8px 30px rgba(16, 185, 129, 0.2)',
        'strong-green': '0 15px 40px rgba(16, 185, 129, 0.25)',
        'card-gray': '0 10px 25px rgba(31, 41, 55, 0.08)',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
          '50%': { transform: 'translateY(-20px) rotate(2deg)' },
        },
        'fade-in': {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'slide-in': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        shine: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
      animation: {
        float: 'float 6s ease-in-out infinite',
        'fade-in': 'fade-in 0.6s ease-out',
        'slide-in': 'slide-in 0.5s ease-out',
        shine: 'shine 0.5s ease-in-out',
      },
      screens: {
        'xs': '475px',
      },
    },
  },
  plugins: [],
}
