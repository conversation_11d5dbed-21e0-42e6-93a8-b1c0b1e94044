{"name": "cashboost-comparison", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true npm run build", "build:analyze": "npm run analyze", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lighthouse": "lighthouse http://localhost:3000 --output html --output-path ./lighthouse-report.html", "perf": "npm run build && npm run lighthouse", "clean": "rm -rf .next out", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\""}, "dependencies": {"next": "^15.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "@types/node": "^22.9.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "typescript": "^5.6.3", "tailwindcss": "^3.4.14", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "clsx": "^2.1.1", "lucide-react": "^0.460.0", "framer-motion": "^11.11.17", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0"}, "devDependencies": {"eslint": "^9.14.0", "eslint-config-next": "^15.0.3", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8"}, "engines": {"node": ">=20.0.0"}}