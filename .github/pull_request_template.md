# 📝 Descrição

<!-- Breve descrição das mudanças realizadas -->

## 🔗 Issue Relacionada

<!-- Se este PR resolve uma issue, referencie aqui -->
Fixes #(número da issue)

## 🎯 Tipo de Mudança

<!-- Marque o tipo de mudança que este PR representa -->

- [ ] 🐛 Bug fix (mudança que corrige um problema)
- [ ] ✨ Nova funcionalidade (mudança que adiciona funcionalidade)
- [ ] 💥 Breaking change (mudança que quebra compatibilidade)
- [ ] 📚 Documentação (mudanças apenas na documentação)
- [ ] 🎨 Estilo (formatação, ponto e vírgula, etc; sem mudança de código)
- [ ] ♻️ Refatoração (mudança de código que não corrige bug nem adiciona funcionalidade)
- [ ] ⚡ Performance (mudança que melhora performance)
- [ ] 🧪 Testes (adição ou correção de testes)
- [ ] 🔧 Chore (mudanças no processo de build, ferramentas auxiliares, etc)

## 🧪 Como Foi Testado?

<!-- Descreva os testes que você executou para verificar suas mudanças -->

- [ ] Testes unitários
- [ ] Testes de integração
- [ ] Testes manuais
- [ ] Testes de acessibilidade
- [ ] Testes de performance

### Ambiente de Teste

- OS: 
- Navegador: 
- Versão do Node: 

## 📱 Screenshots

<!-- Se aplicável, adicione screenshots das mudanças -->

| Antes | Depois |
|-------|--------|
| <!-- screenshot antes --> | <!-- screenshot depois --> |

## ✅ Checklist

<!-- Marque todos os itens que se aplicam -->

### Código
- [ ] Meu código segue os padrões de estilo do projeto
- [ ] Eu realizei uma auto-revisão do meu código
- [ ] Eu comentei meu código, especialmente em áreas difíceis de entender
- [ ] Minhas mudanças não geram novos warnings

### Documentação
- [ ] Eu fiz mudanças correspondentes na documentação
- [ ] Meus comentários estão em português e são claros

### Testes
- [ ] Eu adicionei testes que provam que minha correção é efetiva ou que minha funcionalidade funciona
- [ ] Testes unitários novos e existentes passam localmente com minhas mudanças
- [ ] Qualquer mudança dependente foi mergeada e publicada

### Acessibilidade
- [ ] Eu verifiquei que minhas mudanças são acessíveis
- [ ] Eu testei navegação por teclado
- [ ] Eu verifiquei contraste de cores
- [ ] Eu adicionei ARIA labels quando necessário

### Performance
- [ ] Eu verifiquei que minhas mudanças não impactam negativamente a performance
- [ ] Eu otimizei imagens e assets quando aplicável
- [ ] Eu considerei lazy loading quando apropriado

### Responsividade
- [ ] Eu testei em dispositivos móveis
- [ ] Eu testei em tablets
- [ ] Eu testei em diferentes tamanhos de tela

## 📋 Notas Adicionais

<!-- Qualquer informação adicional que os revisores devem saber -->

## 🔍 Para os Revisores

<!-- Instruções específicas para os revisores -->

- [ ] Foque especialmente em: 
- [ ] Teste especificamente: 
- [ ] Considere o impacto em: 

---

**Obrigado por contribuir! 🎉**
