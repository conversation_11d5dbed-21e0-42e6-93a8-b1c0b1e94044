name: 🐛 Bug Report
description: Reportar um bug ou problema
title: "[BUG] "
labels: ["bug", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Obri<PERSON> por reportar um bug! Por favor, preencha as informações abaixo para nos ajudar a resolver o problema.

  - type: textarea
    id: description
    attributes:
      label: 📝 Descrição do Bug
      description: Uma descrição clara e concisa do bug
      placeholder: Descreva o que aconteceu...
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: 🔄 Passos para Reproduzir
      description: Passos para reproduzir o comportamento
      placeholder: |
        1. Vá para '...'
        2. Clique em '...'
        3. Role para baixo até '...'
        4. Veja o erro
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: ✅ Comportamento Esperado
      description: Uma descrição clara do que você esperava que acontecesse
      placeholder: Descreva o comportamento esperado...
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: ❌ Comportamento Atual
      description: Uma descrição clara do que realmente aconteceu
      placeholder: Descreva o comportamento atual...
    validations:
      required: true

  - type: dropdown
    id: browser
    attributes:
      label: 🌐 Navegador
      description: Em qual navegador você encontrou o bug?
      options:
        - Chrome
        - Firefox
        - Safari
        - Edge
        - Opera
        - Outro
    validations:
      required: true

  - type: dropdown
    id: device
    attributes:
      label: 📱 Dispositivo
      description: Em qual tipo de dispositivo você encontrou o bug?
      options:
        - Desktop
        - Mobile
        - Tablet
    validations:
      required: true

  - type: input
    id: os
    attributes:
      label: 💻 Sistema Operacional
      description: Qual sistema operacional você está usando?
      placeholder: ex. macOS 12.0, Windows 11, Ubuntu 20.04
    validations:
      required: true

  - type: textarea
    id: additional
    attributes:
      label: 📎 Informações Adicionais
      description: Qualquer informação adicional sobre o problema
      placeholder: Screenshots, logs de erro, etc.

  - type: checkboxes
    id: terms
    attributes:
      label: ✅ Checklist
      description: Por favor, confirme que você verificou os seguintes itens
      options:
        - label: Eu verifiquei se este bug já foi reportado
          required: true
        - label: Eu testei na versão mais recente
          required: true
        - label: Eu li o guia de contribuição
          required: true
