{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "'([^']*)'"], ["clsx\\(([^)]*)\\)", "'([^']*)'"]], "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "css.validate": false, "less.validate": false, "scss.validate": false}