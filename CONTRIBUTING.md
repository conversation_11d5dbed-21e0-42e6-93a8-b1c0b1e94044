# 🤝 Guia de Contribuição - CashBoost

Obrigado por considerar contribuir para o CashBoost! Este documento fornece diretrizes para contribuir com o projeto.

## 📋 Índice

- [Código de Conduta](#código-de-conduta)
- [Como Contribuir](#como-contribuir)
- [Configuração do Ambiente](#configuração-do-ambiente)
- [Padrões de Código](#padrões-de-código)
- [Processo de Pull Request](#processo-de-pull-request)
- [Reportar Bugs](#reportar-bugs)
- [Sugerir Funcionalidades](#sugerir-funcionalidades)

## 📜 Código de Conduta

Este projeto adere ao [Código de Conduta do Contributor Covenant](https://www.contributor-covenant.org/). Ao participar, você deve seguir este código.

## 🚀 Como Contribuir

### Tipos de Contribuição

- 🐛 **Correção de Bugs**: Correções para problemas identificados
- ✨ **Novas Funcionalidades**: Implementação de novas features
- 📚 **Documentação**: Melhorias na documentação
- 🎨 **Design/UI**: Melhorias na interface e experiência do usuário
- ⚡ **Performance**: Otimizações de performance
- ♿ **Acessibilidade**: Melhorias de acessibilidade
- 🧪 **Testes**: Adição ou melhoria de testes

### Antes de Começar

1. Verifique se já existe uma issue relacionada
2. Se não existir, crie uma issue descrevendo o problema/funcionalidade
3. Aguarde feedback da equipe antes de começar a implementação

## 🛠️ Configuração do Ambiente

### Pré-requisitos

- Node.js 18+ 
- npm, yarn ou pnpm
- Git

### Configuração

```bash
# 1. Fork o repositório no GitHub

# 2. Clone seu fork
git clone https://github.com/SEU_USUARIO/cashboost.git
cd cashboost

# 3. Adicione o repositório original como upstream
git remote add upstream https://github.com/USUARIO_ORIGINAL/cashboost.git

# 4. Instale as dependências
npm install

# 5. Execute o projeto
npm run dev

# 6. Execute os testes
npm run test
```

## 📝 Padrões de Código

### Estrutura de Arquivos

```
src/
├── components/
│   ├── features/    # Componentes específicos por feature
│   ├── layout/      # Componentes de layout
│   └── ui/          # Componentes reutilizáveis
├── hooks/           # Custom React hooks
├── lib/             # Utilitários e bibliotecas
├── data/            # Dados e mocks
├── types/           # Definições TypeScript
└── constants/       # Constantes da aplicação
```

### Convenções de Nomenclatura

- **Componentes**: PascalCase (`MyComponent.tsx`)
- **Hooks**: camelCase com prefixo `use` (`useMyHook.ts`)
- **Utilitários**: camelCase (`myUtility.ts`)
- **Constantes**: UPPER_SNAKE_CASE (`MY_CONSTANT`)
- **Tipos**: PascalCase (`MyType`)

### Padrões de Código

#### TypeScript
- Use tipagem estrita
- Prefira interfaces sobre types quando possível
- Use generics quando apropriado
- Documente tipos complexos

```typescript
// ✅ Bom
interface User {
  id: string
  name: string
  email: string
}

// ❌ Evitar
const user: any = { ... }
```

#### React
- Use componentes funcionais
- Prefira hooks sobre classes
- Use memo para otimização quando necessário
- Mantenha componentes pequenos e focados

```tsx
// ✅ Bom
export function MyComponent({ title }: { title: string }) {
  return <h1>{title}</h1>
}

// ❌ Evitar
export default function MyComponent(props: any) {
  return <h1>{props.title}</h1>
}
```

#### CSS/Tailwind
- Use classes utilitárias do Tailwind
- Mantenha consistência no design system
- Use variáveis CSS para valores reutilizáveis
- Priorize responsividade mobile-first

```tsx
// ✅ Bom
<div className="flex items-center space-x-4 p-6 bg-white rounded-lg shadow-md">

// ❌ Evitar
<div style={{ display: 'flex', padding: '24px' }}>
```

### Acessibilidade

- Use elementos semânticos HTML
- Inclua ARIA labels quando necessário
- Garanta contraste adequado
- Teste com navegação por teclado
- Teste com screen readers

```tsx
// ✅ Bom
<button 
  aria-label="Fechar modal"
  onClick={onClose}
  className="focus:outline-none focus:ring-2 focus:ring-emerald-500"
>
  <XIcon className="w-5 h-5" />
</button>
```

## 🔄 Processo de Pull Request

### 1. Preparação

```bash
# Atualize seu fork
git checkout main
git pull upstream main
git push origin main

# Crie uma nova branch
git checkout -b feature/minha-funcionalidade
# ou
git checkout -b fix/correcao-bug
```

### 2. Desenvolvimento

- Faça commits pequenos e focados
- Use mensagens de commit descritivas
- Siga o padrão de commits convencionais

```bash
# Exemplos de commits
git commit -m "feat: adiciona componente de busca avançada"
git commit -m "fix: corrige erro de validação no formulário"
git commit -m "docs: atualiza documentação da API"
git commit -m "style: ajusta espaçamento do header"
git commit -m "test: adiciona testes para hook useSearch"
```

### 3. Testes

```bash
# Execute todos os testes
npm run test

# Execute testes de cobertura
npm run test:coverage

# Execute linting
npm run lint

# Execute verificação de tipos
npm run type-check
```

### 4. Pull Request

1. Push da sua branch para seu fork
2. Abra um Pull Request no GitHub
3. Preencha o template de PR
4. Aguarde review da equipe

#### Template de PR

```markdown
## 📝 Descrição

Breve descrição das mudanças realizadas.

## 🔗 Issue Relacionada

Fixes #123

## 🧪 Testes

- [ ] Testes unitários passando
- [ ] Testes de integração passando
- [ ] Testado manualmente
- [ ] Testado em diferentes navegadores

## 📱 Screenshots

(Se aplicável, adicione screenshots das mudanças)

## ✅ Checklist

- [ ] Código segue os padrões do projeto
- [ ] Documentação atualizada
- [ ] Testes adicionados/atualizados
- [ ] Acessibilidade verificada
- [ ] Performance verificada
```

## 🐛 Reportar Bugs

### Antes de Reportar

1. Verifique se o bug já foi reportado
2. Teste na versão mais recente
3. Verifique se não é um problema de configuração

### Template de Bug Report

```markdown
## 🐛 Descrição do Bug

Descrição clara e concisa do bug.

## 🔄 Passos para Reproduzir

1. Vá para '...'
2. Clique em '...'
3. Role para baixo até '...'
4. Veja o erro

## ✅ Comportamento Esperado

Descrição do que deveria acontecer.

## 📱 Ambiente

- OS: [ex: macOS 12.0]
- Navegador: [ex: Chrome 95.0]
- Versão do Node: [ex: 18.0.0]
- Versão do projeto: [ex: 1.0.0]

## 📎 Informações Adicionais

Qualquer informação adicional sobre o problema.
```

## 💡 Sugerir Funcionalidades

### Template de Feature Request

```markdown
## 🚀 Descrição da Funcionalidade

Descrição clara da funcionalidade desejada.

## 🎯 Problema que Resolve

Qual problema esta funcionalidade resolve?

## 💭 Solução Proposta

Como você imagina que esta funcionalidade deveria funcionar?

## 🔄 Alternativas Consideradas

Outras soluções que você considerou?

## 📎 Informações Adicionais

Qualquer informação adicional sobre a funcionalidade.
```

## 🏆 Reconhecimento

Todos os contribuidores serão reconhecidos no README do projeto. Obrigado por ajudar a tornar o CashBoost melhor!

## 📞 Contato

Se você tiver dúvidas sobre como contribuir, sinta-se à vontade para:

- Abrir uma issue com a tag `question`
- Entrar em contato através do email: [<EMAIL>]

---

**Obrigado por contribuir! 🎉**
