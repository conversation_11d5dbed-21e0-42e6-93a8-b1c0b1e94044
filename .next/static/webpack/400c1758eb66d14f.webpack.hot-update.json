{"c": ["app/layout", "app/page", "webpack"], "r": ["app/_not-found/page"], "m": ["(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs", "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Flucas%2Fworkspace%2Fnovo%2Fsrc%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Flucas%2Fworkspace%2Fnovo%2Fsrc%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Flucas%2Fworkspace%2Fnovo%2Fsrc%2Fcomponents%2FHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Flucas%2Fworkspace%2Fnovo%2Fsrc%2Fcomponents%2FHowItWorksSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Flucas%2Fworkspace%2Fnovo%2Fsrc%2Fcomponents%2FPlatformComparisonSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Flucas%2Fworkspace%2Fnovo%2Fsrc%2Fcomponents%2FTopRatesSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Flucas%2Fworkspace%2Fnovo%2Fsrc%2Fcomponents%2FTrustSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js", "(app-pages-browser)/./src/components/Header.tsx", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Flucas%2Fworkspace%2Fnovo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&page=%2F_not-found%2Fpage!", "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js", "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js"]}