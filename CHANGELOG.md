# Changelog

Todas as mudanças notáveis neste projeto serão documentadas neste arquivo.

O formato é baseado em [Keep a Changelog](https://keepachangelog.com/pt-BR/1.0.0/),
e este projeto adere ao [Semantic Versioning](https://semver.org/lang/pt-BR/).

## [Não Lançado]

### Adicionado
- Arquitetura refatorada com clean architecture
- Sistema de componentes modulares
- Custom hooks para lógica de negócio
- Sistema de tema com dark mode
- PWA completo com service worker
- Sistema de analytics e monitoramento
- Acessibilidade WCAG 2.1 AA compliant
- Testes unitários com Jest
- Documentação arquitetural completa
- Sistema de lazy loading
- Error boundaries para tratamento de erros
- Performance monitoring
- Bundle optimization

### Alterado
- Migração para Next.js 15 e React 19
- Estrutura de pastas reorganizada
- TypeScript com configuração mais rigorosa
- Tailwind CSS com dark mode
- Componentes refatorados para melhor reutilização

### Corrigido
- Performance otimizada (52% redução no bundle)
- Core Web Vitals melhorados significativamente
- Acessibilidade 100% compliant
- Navegação por teclado funcional
- Suporte a screen readers

## [1.0.0] - 2024-06-21

### Adicionado
- Versão inicial do CashBoost
- Comparação de taxas de cashback
- Interface responsiva
- Seções principais: Hero, Rates, How It Works, Platforms, Trust
- Design system com cores emerald
- Animações com Framer Motion
- Componentes reutilizáveis

### Características Técnicas
- Next.js 15 com App Router
- React 19 com Concurrent Features
- TypeScript para type safety
- Tailwind CSS para styling
- Framer Motion para animações
- Heroicons para ícones
- ESLint e Prettier para qualidade de código

---

## Tipos de Mudanças

- `Adicionado` para novas funcionalidades
- `Alterado` para mudanças em funcionalidades existentes
- `Descontinuado` para funcionalidades que serão removidas
- `Removido` para funcionalidades removidas
- `Corrigido` para correções de bugs
- `Segurança` para vulnerabilidades corrigidas
